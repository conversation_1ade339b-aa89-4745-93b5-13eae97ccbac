// ipcHandlers.ts

import { ipcMain, shell, app } from "electron"
import { IIpcHandlerDeps } from "./main"
import { aiConfigService } from "./services/AIConfigService"
import { performanceMonitor } from "./services/PerformanceMonitor"
import { openaiService } from "./services/openaiService"

export function initializeIpcHandlers(deps: IIpcHandlerDeps): void {
  // console.log("Initializing IPC handlers") // 减少日志输出

  // Credits handlers removed - unlimited usage

  // Screenshot queue handlers
  ipcMain.handle("get-screenshot-queue", () => {
    return deps.getScreenshotQueue()
  })

  ipcMain.handle("get-extra-screenshot-queue", () => {
    return deps.getExtraScreenshotQueue()
  })

  ipcMain.handle("delete-screenshot", async (event, path: string) => {
    return deps.deleteScreenshot(path)
  })

  ipcMain.handle("get-image-preview", async (event, path: string) => {
    return deps.getImagePreview(path)
  })

  // Screenshot processing handlers
  ipcMain.handle("process-screenshots", async () => {
    await deps.processingHelper?.processScreenshots()
  })

  // Window dimension handlers
  ipcMain.handle(
    "update-content-dimensions",
    async (event, { width, height }: { width: number; height: number }) => {
      if (width && height) {
        deps.setWindowDimensions(width, height)
      }
    }
  )

  ipcMain.handle(
    "set-window-dimensions",
    (event, width: number, height: number) => {
      deps.setWindowDimensions(width, height)
    }
  )

  // Screenshot management handlers
  ipcMain.handle("get-screenshots", async () => {
    try {
      let previews = []
      const currentView = deps.getView()

      if (currentView === "queue") {
        const queue = deps.getScreenshotQueue()
        previews = await Promise.all(
          queue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      } else {
        const extraQueue = deps.getExtraScreenshotQueue()
        previews = await Promise.all(
          extraQueue.map(async (path) => ({
            path,
            preview: await deps.getImagePreview(path)
          }))
        )
      }

      return previews
    } catch (error) {
      console.error("Error getting screenshots:", error)
      throw error
    }
  })

  // Screenshot trigger handlers
  ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      try {
        const screenshotPath = await deps.takeScreenshot()
        const preview = await deps.getImagePreview(screenshotPath)
        mainWindow.webContents.send("screenshot-taken", {
          path: screenshotPath,
          preview
        })
        return { success: true }
      } catch (error) {
        console.error("Error triggering screenshot:", error)
        return { error: "Failed to trigger screenshot" }
      }
    }
    return { error: "No main window available" }
  })

  ipcMain.handle("take-screenshot", async () => {
    try {
      const screenshotPath = await deps.takeScreenshot()
      const preview = await deps.getImagePreview(screenshotPath)
      return { path: screenshotPath, preview }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      return { error: "Failed to take screenshot" }
    }
  })

  // Auth handlers removed - no authentication needed

  ipcMain.handle("open-external-url", (event, url: string) => {
    shell.openExternal(url)
  })

  // Subscription handlers removed - no authentication needed

  // Window management handlers
  ipcMain.handle("toggle-window", () => {
    try {
      deps.toggleMainWindow()
      return { success: true }
    } catch (error) {
      console.error("Error toggling window:", error)
      return { error: "Failed to toggle window" }
    }
  })

  ipcMain.handle("reset-queues", async () => {
    try {
      deps.clearQueues()
      return { success: true }
    } catch (error) {
      console.error("Error resetting queues:", error)
      return { error: "Failed to reset queues" }
    }
  })

  // Process screenshot handlers
  ipcMain.handle("trigger-process-screenshots", async () => {
    try {
      await deps.processingHelper?.processScreenshots()
      return { success: true }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      return { error: "Failed to process screenshots" }
    }
  })

  // Reset handlers
  ipcMain.handle("trigger-reset", () => {
    try {
      // First cancel any ongoing requests
      deps.processingHelper?.cancelOngoingRequests()

      // Clear all queues immediately
      deps.clearQueues()

      // Reset view to queue
      deps.setView("queue")

      // Get main window and send reset events
      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        // Send reset events in sequence
        mainWindow.webContents.send("reset-view")
        mainWindow.webContents.send("reset")
      }

      return { success: true }
    } catch (error) {
      console.error("Error triggering reset:", error)
      return { error: "Failed to trigger reset" }
    }
  })

  // Window movement handlers
  ipcMain.handle("trigger-move-left", () => {
    try {
      deps.moveWindowLeft()
      return { success: true }
    } catch (error) {
      console.error("Error moving window left:", error)
      return { error: "Failed to move window left" }
    }
  })

  ipcMain.handle("trigger-move-right", () => {
    try {
      deps.moveWindowRight()
      return { success: true }
    } catch (error) {
      console.error("Error moving window right:", error)
      return { error: "Failed to move window right" }
    }
  })

  ipcMain.handle("trigger-move-up", () => {
    try {
      deps.moveWindowUp()
      return { success: true }
    } catch (error) {
      console.error("Error moving window up:", error)
      return { error: "Failed to move window up" }
    }
  })

  ipcMain.handle("trigger-move-down", () => {
    try {
      deps.moveWindowDown()
      return { success: true }
    } catch (error) {
      console.error("Error moving window down:", error)
      return { error: "Failed to move window down" }
    }
  })

  // AI Configuration handlers
  ipcMain.handle("get-ai-config", () => {
    try {
      return aiConfigService.getConfig()
    } catch (error) {
      console.error("Error getting AI config:", error)
      return null
    }
  })

  ipcMain.handle("update-ai-config", (event, config) => {
    try {
      aiConfigService.updateConfig(config)
      // 重新初始化OpenAI服务以使用新配置
      openaiService.reinitialize()
      return { success: true }
    } catch (error) {
      console.error("Error updating AI config:", error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle("reset-ai-config", () => {
    try {
      aiConfigService.resetToDefault()
      // 重新初始化OpenAI服务以使用默认配置
      openaiService.reinitialize()
      return { success: true }
    } catch (error) {
      console.error("Error resetting AI config:", error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle("optimize-ai-config", () => {
    try {
      const stats = performanceMonitor.getStats()
      aiConfigService.autoOptimize(stats)
      return { success: true }
    } catch (error) {
      console.error("Error optimizing AI config:", error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle("test-ai-connection", async () => {
    try {
      return await openaiService.testConnection()
    } catch (error) {
      console.error("Error testing AI connection:", error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle("get-available-models", async () => {
    try {
      return await openaiService.getAvailableModels()
    } catch (error) {
      console.error("Error getting available models:", error)
      return { success: false, error: error.message }
    }
  })

  ipcMain.handle("clear-models-cache", async () => {
    try {
      openaiService.clearModelsCache()
      return { success: true }
    } catch (error) {
      console.error("Error clearing models cache:", error)
      return { success: false, error: error.message }
    }
  })



  ipcMain.handle("check-api-health", async () => {
    try {
      return await openaiService.checkAPIHealth()
    } catch (error) {
      console.error("Error checking API health:", error)
      return { healthy: false, details: { error: error.message } }
    }
  })

  // Performance monitoring handlers
  ipcMain.handle("get-performance-stats", () => {
    try {
      return performanceMonitor.getStats()
    } catch (error) {
      console.error("Error getting performance stats:", error)
      return null
    }
  })

  ipcMain.handle("get-performance-report", () => {
    try {
      return performanceMonitor.getDetailedReport()
    } catch (error) {
      console.error("Error getting performance report:", error)
      return null
    }
  })

  ipcMain.handle("clear-performance-data", () => {
    try {
      performanceMonitor.cleanup(0) // Clear all data
      return { success: true }
    } catch (error) {
      console.error("Error clearing performance data:", error)
      return { success: false, error: error.message }
    }
  })

  // Application control handlers
  ipcMain.handle("quit-app", () => {
    try {
      console.log("Quit app requested from renderer")
      app.quit()
      return { success: true }
    } catch (error) {
      console.error("Error quitting app:", error)
      return { success: false, error: error.message }
    }
  })
}
