import React, { useState, useRef, useEffect } from "react"

interface CustomLanguageSelectorProps {
  currentLanguage: string
  setLanguage: (language: string) => void
}

const languages = [
  { value: "python", label: "Python" },
  { value: "javascript", label: "JavaScript" },
  { value: "java", label: "Java" },
  { value: "golang", label: "Go" },
  { value: "csharp", label: "C#" },
  { value: "rust", label: "Rust" },
  { value: "cpp", label: "C++" },
  { value: "swift", label: "Swift" },
  { value: "kotlin", label: "<PERSON><PERSON><PERSON>" },
  { value: "ruby", label: "Ruby" },
  { value: "sql", label: "SQL" },
  { value: "r", label: "R" }
]

export const CustomLanguageSelector: React.FC<CustomLanguageSelectorProps> = ({
  currentLanguage,
  setLanguage
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const handleLanguageSelect = (language: string) => {
    window.__LANGUAGE__ = language
    setLanguage(language)
    setIsOpen(false)
  }

  const currentLang = languages.find(lang => lang.value === currentLanguage)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  useEffect(() => {
    // Update window dimensions when dropdown opens/closes
    if (isOpen) {
      const dropdownHeight = languages.length * 28 + 16 // 28px per item + padding
      window.electronAPI?.updateContentDimensions({
        width: Math.max(window.innerWidth, 400),
        height: window.innerHeight + Math.min(dropdownHeight, 350)
      })
    } else {
      // Reset dimensions
      setTimeout(() => {
        const contentElement = document.querySelector('.bg-transparent')
        if (contentElement) {
          window.electronAPI?.updateContentDimensions({
            width: contentElement.scrollWidth,
            height: contentElement.scrollHeight
          })
        }
      }, 100)
    }
  }, [isOpen])

  return (
    <div className="mb-3 px-2 space-y-1">
      <div className="flex items-center justify-between text-[13px] font-medium text-white/90">
        <span>Language</span>
        <div ref={dropdownRef} className="relative">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="bg-white/10 rounded px-2 py-1 text-sm outline-none border border-white/10 focus:border-white/20 cursor-pointer text-white flex items-center justify-between"
            style={{ 
              minWidth: '120px',
              transition: 'all 0.2s'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.15)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'rgba(255, 255, 255, 0.1)'}
          >
            <span>{currentLang?.label || "Select"}</span>
            <svg
              className={`w-3 h-3 ml-2 transition-transform ${isOpen ? 'rotate-180' : ''}`}
              xmlns="http://www.w3.org/2000/svg"
              width="12" 
              height="12" 
              viewBox="0 0 12 12"
            >
              <path 
                fill="#ffffff" 
                d="M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z"
              />
            </svg>
          </button>
          
          {isOpen && (
            <div className="absolute top-full right-0 mt-1 min-w-[120px] backdrop-blur-md bg-black/80 rounded-lg border border-white/10 overflow-hidden z-50">
              <div className="py-1">
                {languages.map((lang) => (
                  <button
                    key={lang.value}
                    onClick={() => handleLanguageSelect(lang.value)}
                    className={`w-full px-3 py-1.5 text-sm text-left hover:bg-white/10 transition-colors text-white/90 ${
                      currentLanguage === lang.value ? 'bg-white/10' : ''
                    }`}
                  >
                    {lang.label}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}