// PerformanceMonitor.ts - AI性能监控和分析
export interface PerformanceMetric {
  operation: string
  startTime: number
  endTime?: number
  duration?: number
  success: boolean
  error?: string
  metadata?: any
}

export interface PerformanceStats {
  totalOperations: number
  successRate: number
  averageDuration: number
  cacheHitRate: number
  errorsByType: Record<string, number>
  operationStats: Record<string, {
    count: number
    averageDuration: number
    successRate: number
  }>
}

export class PerformanceMonitor {
  private metrics: PerformanceMetric[] = []
  private activeOperations: Map<string, PerformanceMetric> = new Map()
  private readonly MAX_METRICS = 500
  private cleanupTimer?: NodeJS.Timeout

  constructor() {
    // 启动定期清理定时器
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, 10 * 60 * 1000)
  }

  /**
   * 开始监控操作
   */
  startOperation(operation: string, metadata?: any): string {
    const operationId = `${operation}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

    // 清理元数据中的敏感信息
    const sanitizedMetadata = this.sanitizeMetadata(metadata)

    const metric: PerformanceMetric = {
      operation,
      startTime: Date.now(),
      success: false,
      metadata: sanitizedMetadata
    }

    this.activeOperations.set(operationId, metric)
    console.log(`[Performance] Started ${operation}`)

    // 定期清理
    if (this.metrics.length > this.MAX_METRICS * 1.2) {
      this.cleanup()
    }

    return operationId
  }

  /**
   * 清理元数据中的敏感信息
   */
  private sanitizeMetadata(metadata?: any): any {
    if (!metadata) return metadata

    const sanitized = { ...metadata }

    // 移除或脱敏敏感字段
    if (sanitized.apiKey) {
      sanitized.apiKey = '***REDACTED***'
    }
    if (sanitized.problemInfo?.description) {
      // 只保留描述的长度信息，不保留具体内容
      sanitized.problemInfo = {
        ...sanitized.problemInfo,
        description: `[${sanitized.problemInfo.description.length} chars]`
      }
    }

    return sanitized
  }

  /**
   * 结束监控操作
   */
  endOperation(operationId: string, success: boolean, error?: string): void {
    const metric = this.activeOperations.get(operationId)
    if (!metric) {
      console.warn(`[Performance] Operation ${operationId} not found`)
      return
    }

    metric.endTime = Date.now()
    metric.duration = metric.endTime - metric.startTime
    metric.success = success
    metric.error = error

    // 移动到历史记录
    this.metrics.push(metric)
    this.activeOperations.delete(operationId)

    // 清理旧记录
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS)
    }

    console.log(`[Performance] Completed ${metric.operation} in ${metric.duration}ms (${success ? 'success' : 'failed'})`)
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(operation: string): void {
    const metric: PerformanceMetric = {
      operation: `${operation}_cache_hit`,
      startTime: Date.now(),
      endTime: Date.now(),
      duration: 0,
      success: true,
      metadata: { cached: true }
    }

    this.metrics.push(metric)
    console.log(`[Performance] Cache hit for ${operation}`)
  }

  /**
   * 获取性能统计
   */
  getStats(timeWindow?: number): PerformanceStats {
    const now = Date.now()
    const windowStart = timeWindow ? now - timeWindow : 0
    
    const relevantMetrics = this.metrics.filter(m => 
      m.startTime >= windowStart && m.endTime !== undefined
    )

    if (relevantMetrics.length === 0) {
      return {
        totalOperations: 0,
        successRate: 0,
        averageDuration: 0,
        cacheHitRate: 0,
        errorsByType: {},
        operationStats: {}
      }
    }

    const totalOperations = relevantMetrics.length
    const successfulOperations = relevantMetrics.filter(m => m.success).length
    const successRate = (successfulOperations / totalOperations) * 100

    const durations = relevantMetrics
      .filter(m => m.duration !== undefined)
      .map(m => m.duration!)
    const averageDuration = durations.length > 0 
      ? durations.reduce((a, b) => a + b, 0) / durations.length 
      : 0

    const cacheHits = relevantMetrics.filter(m => m.metadata?.cached).length
    const cacheHitRate = (cacheHits / totalOperations) * 100

    // 错误统计
    const errorsByType: Record<string, number> = {}
    relevantMetrics
      .filter(m => !m.success && m.error)
      .forEach(m => {
        const errorType = this.categorizeError(m.error!)
        errorsByType[errorType] = (errorsByType[errorType] || 0) + 1
      })

    // 操作统计
    const operationStats: Record<string, any> = {}
    const operationGroups = this.groupBy(relevantMetrics, m => m.operation.replace(/_cache_hit$/, ''))
    
    for (const [operation, metrics] of Object.entries(operationGroups)) {
      const count = metrics.length
      const successful = metrics.filter(m => m.success).length
      const durations = metrics
        .filter(m => m.duration !== undefined)
        .map(m => m.duration!)
      
      operationStats[operation] = {
        count,
        averageDuration: durations.length > 0 
          ? durations.reduce((a, b) => a + b, 0) / durations.length 
          : 0,
        successRate: (successful / count) * 100
      }
    }

    return {
      totalOperations,
      successRate,
      averageDuration,
      cacheHitRate,
      errorsByType,
      operationStats
    }
  }

  /**
   * 获取详细报告
   */
  getDetailedReport(timeWindow: number = 24 * 60 * 60 * 1000): string {
    const stats = this.getStats(timeWindow)
    const hours = timeWindow / (60 * 60 * 1000)

    let report = `\n=== AI Performance Report (Last ${hours}h) ===\n`
    report += `Total Operations: ${stats.totalOperations}\n`
    report += `Success Rate: ${stats.successRate.toFixed(1)}%\n`
    report += `Average Duration: ${stats.averageDuration.toFixed(0)}ms\n`
    report += `Cache Hit Rate: ${stats.cacheHitRate.toFixed(1)}%\n\n`

    if (Object.keys(stats.errorsByType).length > 0) {
      report += `Errors by Type:\n`
      for (const [type, count] of Object.entries(stats.errorsByType)) {
        report += `  ${type}: ${count}\n`
      }
      report += `\n`
    }

    report += `Operation Statistics:\n`
    for (const [operation, opStats] of Object.entries(stats.operationStats)) {
      report += `  ${operation}:\n`
      report += `    Count: ${opStats.count}\n`
      report += `    Avg Duration: ${opStats.averageDuration.toFixed(0)}ms\n`
      report += `    Success Rate: ${opStats.successRate.toFixed(1)}%\n`
    }

    return report
  }

  /**
   * 清理旧数据
   */
  cleanup(maxAge: number = 2 * 60 * 60 * 1000): void {
    const cutoff = Date.now() - maxAge
    const originalLength = this.metrics.length

    // 按时间过滤
    this.metrics = this.metrics.filter(m => m.startTime >= cutoff)

    // 如果仍然太多，保留最新的记录
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS)
    }

    // 清理长时间未完成的活动操作
    const activeOperationsCutoff = Date.now() - (30 * 60 * 1000)
    for (const [operationId, metric] of this.activeOperations.entries()) {
      if (metric.startTime < activeOperationsCutoff) {
        console.warn(`[Performance] Removing stale active operation: ${operationId}`)
        this.activeOperations.delete(operationId)
      }
    }

    if (originalLength !== this.metrics.length) {
      console.log(`[Performance] Cleaned up ${originalLength - this.metrics.length} old metrics, ${this.metrics.length} remaining`)
    }
  }

  /**
   * 销毁性能监控器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    this.metrics = []
    this.activeOperations.clear()
    console.log('[Performance] Performance monitor destroyed')
  }

  /**
   * 导出数据
   */
  exportData(): PerformanceMetric[] {
    return [...this.metrics]
  }

  /**
   * 分类错误
   */
  private categorizeError(error: string): string {
    if (error.includes('API key')) return 'API_KEY_ERROR'
    if (error.includes('rate limit')) return 'RATE_LIMIT_ERROR'
    if (error.includes('timeout')) return 'TIMEOUT_ERROR'
    if (error.includes('network')) return 'NETWORK_ERROR'
    if (error.includes('JSON') || error.includes('parse')) return 'PARSING_ERROR'
    return 'UNKNOWN_ERROR'
  }

  /**
   * 分组辅助函数
   */
  private groupBy<T>(array: T[], keyFn: (item: T) => string): Record<string, T[]> {
    return array.reduce((groups, item) => {
      const key = keyFn(item)
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(item)
      return groups
    }, {} as Record<string, T[]>)
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitor()
