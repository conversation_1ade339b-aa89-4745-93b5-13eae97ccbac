// Simple logger utility that only logs in development mode
const isDev = !process.env.NODE_ENV || process.env.NODE_ENV === 'development'

export const logger = {
  log: (...args: any[]) => {
    if (isDev) {
      console.log('[Main]', ...args)
    }
  },
  
  error: (...args: any[]) => {
    // Always log errors
    console.error('[Main Error]', ...args)
  },
  
  warn: (...args: any[]) => {
    if (isDev) {
      console.warn('[Main Warning]', ...args)
    }
  },
  
  debug: (...args: any[]) => {
    if (isDev && process.env.DEBUG) {
      console.debug('[Main Debug]', ...args)
    }
  }
}