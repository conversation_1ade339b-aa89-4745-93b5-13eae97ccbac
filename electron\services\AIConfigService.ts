// AIConfigService.ts - AI配置管理和动态优化
import fs from 'fs'
import path from 'path'
import { app } from 'electron'

export interface AIConfig {
  // API 配置
  apiUrl: string
  apiKey: string
  model: string

  // 基础配置
  temperature: number
  maxTokens: {
    problemExtraction: number
    solutionGeneration: number
    debugging: number
  }
  timeout: {
    problemExtraction: number
    solutionGeneration: number
    debugging: number
  }

  // 重试配置
  retry: {
    maxAttempts: number
    baseDelay: number
    maxDelay: number
    backoffMultiplier: number
  }

  // 缓存配置
  cache: {
    enabled: boolean
    ttl: number
    maxEntries: number
  }

  // 性能优化
  performance: {
    enableBatching: boolean
    batchSize: number
    enableCompression: boolean
    enableParallelProcessing: boolean
  }

  // 质量控制
  quality: {
    enableValidation: boolean
    minConfidenceScore: number
    enableFallbackModel: boolean
    fallbackModel: string
  }
}

export class AIConfigService {
  private config: AIConfig
  private configPath: string
  private readonly DEFAULT_CONFIG: AIConfig = {
    apiUrl: 'https://api.openai.com/v1',
    apiKey: '',
    model: 'gpt-4o',
    temperature: 0.0,
    maxTokens: {
      problemExtraction: 1000,
      solutionGeneration: 2000,
      debugging: 1500
    },
    timeout: {
      problemExtraction: 20000,
      solutionGeneration: 30000,
      debugging: 25000
    },
    retry: {
      maxAttempts: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2
    },
    cache: {
      enabled: true,
      ttl: 2 * 60 * 60 * 1000,
      maxEntries: 100
    },
    performance: {
      enableBatching: false,
      batchSize: 3,
      enableCompression: true,
      enableParallelProcessing: false
    },
    quality: {
      enableValidation: true,
      minConfidenceScore: 0.8,
      enableFallbackModel: true,
      fallbackModel: 'gpt-4o-mini'
    }
  }

  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'ai_config.json')
    this.config = this.loadConfig()
  }

  /**
   * 加载配置
   */
  private loadConfig(): AIConfig {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8')
        const loadedConfig = JSON.parse(configData)
        
        // 合并默认配置和加载的配置
        const mergedConfig = this.mergeConfigs(this.DEFAULT_CONFIG, loadedConfig)
        // console.log('[AI Config] Loaded configuration from file') // 减少日志输出
        return mergedConfig
      }
    } catch (error) {
      console.error('[AI Config] Error loading config:', error)
    }
    
    console.log('[AI Config] Using default configuration')
    return { ...this.DEFAULT_CONFIG }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      // 创建配置的安全副本，不包含敏感信息用于日志
      const configForLog = { ...this.config }
      if (configForLog.apiKey) {
        configForLog.apiKey = '***CONFIGURED***'
      }

      fs.writeFileSync(this.configPath, JSON.stringify(this.config, null, 2), { mode: 0o600 })
      // console.log('[AI Config] Configuration saved') // 减少日志输出
    } catch (error) {
      console.error('[AI Config] Error saving config:', error)
    }
  }

  /**
   * 深度合并配置对象
   */
  private mergeConfigs(defaultConfig: any, userConfig: any): any {
    const result = { ...defaultConfig }
    
    for (const key in userConfig) {
      if (userConfig[key] !== null && typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        result[key] = this.mergeConfigs(defaultConfig[key] || {}, userConfig[key])
      } else {
        result[key] = userConfig[key]
      }
    }
    
    return result
  }

  /**
   * 获取当前配置
   */
  getConfig(): AIConfig {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<AIConfig>): void {
    // 验证和清理输入
    const sanitizedUpdates = this.sanitizeAndValidateConfig(updates)

    this.config = this.mergeConfigs(this.config, sanitizedUpdates)
    this.saveConfig()

  }

  /**
   * 清理和验证配置输入
   */
  private sanitizeAndValidateConfig(config: Partial<AIConfig>): Partial<AIConfig> {
    const sanitized: Partial<AIConfig> = {}

    // 验证API URL
    if (config.apiUrl !== undefined) {
      const url = config.apiUrl.trim()
      if (url && this.isValidUrl(url)) {
        sanitized.apiUrl = url
      } else if (url) {
        throw new Error('Invalid API URL format')
      }
    }

    // 验证API密钥
    if (config.apiKey !== undefined) {
      const key = config.apiKey.trim()
      if (key && key.length < 10) {
        throw new Error('API key too short')
      }
      sanitized.apiKey = key
    }

    // 验证模型名称
    if (config.model !== undefined) {
      const model = config.model.trim()
      if (model && !/^[a-zA-Z0-9\-_.]+$/.test(model)) {
        throw new Error('Invalid model name format')
      }
      sanitized.model = model
    }

    // 复制其他安全的配置项
    if (config.temperature !== undefined) sanitized.temperature = config.temperature
    if (config.maxTokens !== undefined) sanitized.maxTokens = config.maxTokens
    if (config.timeout !== undefined) sanitized.timeout = config.timeout
    if (config.retry !== undefined) sanitized.retry = config.retry
    if (config.cache !== undefined) sanitized.cache = config.cache
    if (config.performance !== undefined) sanitized.performance = config.performance
    if (config.quality !== undefined) sanitized.quality = config.quality

    return sanitized
  }

  /**
   * 验证URL格式
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url)
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
    } catch {
      return false
    }
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    this.config = { ...this.DEFAULT_CONFIG }
    this.saveConfig()
    console.log('[AI Config] Configuration reset to default')
  }

  /**
   * 根据性能数据自动优化配置
   */
  autoOptimize(performanceStats: any): void {
    const updates: Partial<AIConfig> = {}

    // 根据成功率调整重试配置
    if (performanceStats.successRate < 80) {
      updates.retry = {
        ...this.config.retry,
        maxAttempts: Math.min(this.config.retry.maxAttempts + 1, 5)
      }
      console.log('[AI Config] Increased retry attempts due to low success rate')
    }

    // 根据平均响应时间调整超时配置
    if (performanceStats.averageDuration > 60000) {
      updates.timeout = {
        problemExtraction: Math.max(this.config.timeout.problemExtraction * 1.2, 90000),
        solutionGeneration: Math.max(this.config.timeout.solutionGeneration * 1.2, 120000),
        debugging: Math.max(this.config.timeout.debugging * 1.2, 100000)
      }
      console.log('[AI Config] Increased timeouts due to slow response times')
    }

    // 根据缓存命中率调整缓存配置
    if (performanceStats.cacheHitRate > 50) {
      updates.cache = {
        ...this.config.cache,
        ttl: Math.min(this.config.cache.ttl * 1.5, 7 * 24 * 60 * 60 * 1000) // 最多7天
      }
      console.log('[AI Config] Increased cache TTL due to high hit rate')
    }

    if (Object.keys(updates).length > 0) {
      this.updateConfig(updates)
    }
  }

  /**
   * 获取特定操作的配置
   */
  getOperationConfig(operation: 'problemExtraction' | 'solutionGeneration' | 'debugging') {
    return {
      model: this.config.model,
      temperature: this.config.temperature,
      maxTokens: this.config.maxTokens[operation],
      timeout: this.config.timeout[operation],
      retry: this.config.retry,
      enableValidation: this.config.quality.enableValidation,
      fallbackModel: this.config.quality.enableFallbackModel ? this.config.quality.fallbackModel : null
    }
  }

  /**
   * 验证配置有效性
   */
  validateConfig(config: Partial<AIConfig>): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (config.temperature !== undefined && (config.temperature < 0 || config.temperature > 2)) {
      errors.push('Temperature must be between 0 and 2')
    }

    if (config.maxTokens) {
      Object.values(config.maxTokens).forEach((tokens) => {
        if (tokens < 100 || tokens > 200000) {
          errors.push(`Max tokens must be between 100 and 200000`)
        }
      })
    }

    if (config.retry?.maxAttempts !== undefined && (config.retry.maxAttempts < 1 || config.retry.maxAttempts > 10)) {
      errors.push('Max retry attempts must be between 1 and 10')
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): { success: boolean; error?: string } {
    try {
      const importedConfig = JSON.parse(configJson)
      const validation = this.validateConfig(importedConfig)
      
      if (!validation.valid) {
        return {
          success: false,
          error: `Invalid configuration: ${validation.errors.join(', ')}`
        }
      }

      this.updateConfig(importedConfig)
      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: `Failed to parse configuration: ${error}`
      }
    }
  }
}

// 单例实例
export const aiConfigService = new AIConfigService()
