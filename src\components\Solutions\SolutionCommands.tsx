import React, { useState, useEffect } from "react"
import { useToast } from "../../contexts/toast"
import { Screenshot } from "../../types/screenshots"
import { COMMAND_KEY } from "../../utils/platform"
import SettingsPanel from "../shared/SettingsPanel"

export interface SolutionCommandsProps {
  onTooltipVisibilityChange: (visible: boolean, height: number) => void
  isProcessing: boolean
  screenshots?: Screenshot[]
  extraScreenshots?: Screenshot[]
  credits: number
  currentLanguage: string
  setLanguage: (language: string) => void
}

const SolutionCommands: React.FC<SolutionCommandsProps> = ({
  onTooltipVisibilityChange,
  isProcessing,
  extraScreenshots = [],
  credits,
  currentLanguage,
  setLanguage
}) => {
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const { showToast } = useToast()

  useEffect(() => {
    console.log('SolutionCommands component mounted')
    if (onTooltipVisibilityChange) {
      // Settings panel affects main window height when open
      if (isSettingsOpen) {
        onTooltipVisibilityChange(true, 280) // Approximate height of settings panel
      } else {
        onTooltipVisibilityChange(false, 0)
      }
    }
  }, [onTooltipVisibilityChange, isSettingsOpen])

  const handleSettingsClick = () => {
    console.log('Settings clicked - toggling panel')
    setIsSettingsOpen(!isSettingsOpen)
  }

  const handleTakeScreenshot = async () => {
    try {
      const result = await window.electronAPI.takeScreenshot()
      if (!result.success) {
        console.error("Failed to take screenshot:", result.error)
        showToast("Error", "Failed to take screenshot", "error")
      }
    } catch (error) {
      console.error("Error taking screenshot:", error)
      showToast("Error", "Failed to take screenshot", "error")
    }
  }

  const handleSolve = async () => {
    if (credits <= 0) {
      showToast(
        "Out of Credits",
        "You are out of credits. Please refill at https://www.interviewcoder.co/settings.",
        "error"
      )
      return
    }

    if (extraScreenshots.length === 0) {
      showToast("No Screenshots", "Please take a screenshot first", "error")
      return
    }

    try {
      const result = await window.electronAPI.triggerProcessScreenshots()
      if (!result.success) {
        console.error("Failed to process screenshots:", result.error)
        showToast("Error", "Failed to process screenshots", "error")
      }
    } catch (error) {
      console.error("Error processing screenshots:", error)
      showToast("Error", "Failed to process screenshots", "error")
    }
  }

  const handleStartOver = async () => {
    try {
      const result = await window.electronAPI.resetView()
      if (!result.success) {
        console.error("Failed to reset view:", result.error)
        showToast("Error", "Failed to reset view", "error")
      }
    } catch (error) {
      console.error("Error resetting view:", error)
      showToast("Error", "Failed to reset view", "error")
    }
  }

  const handleToggleWindow = async () => {
    try {
      const result = await window.electronAPI.toggleMainWindow()
      if (!result.success) {
        console.error("Failed to toggle window:", result.error)
        showToast("Error", "Failed to toggle window", "error")
      }
    } catch (error) {
      console.error("Error toggling window:", error)
      showToast("Error", "Failed to toggle window", "error")
    }
  }

  return (
    <div>
      <div className="w-fit">
        <div className="text-xs text-white/90 backdrop-blur-md bg-black/60 rounded-lg py-1.5 px-3 flex items-center justify-center gap-3">
          {/* Show/Hide - Always visible */}
          <div
            className="flex items-center gap-1.5 cursor-pointer rounded px-1.5 py-1 hover:bg-white/10 transition-colors"
            onClick={async () => {
              try {
                const result = await window.electronAPI.toggleMainWindow()
                if (!result.success) {
                  console.error("Failed to toggle window:", result.error)
                  showToast("Error", "Failed to toggle window", "error")
                }
              } catch (error) {
                console.error("Error toggling window:", error)
                showToast("Error", "Failed to toggle window", "error")
              }
            }}
          >
            <span className="text-[11px] leading-none">Show/Hide</span>
            <div className="flex gap-0.5">
              <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                {COMMAND_KEY}
              </button>
              <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                B
              </button>
            </div>
          </div>

          {/* Screenshot and Debug commands - Only show if not processing */}
          {!isProcessing && (
            <>
              <div className="mx-1 h-4 w-px bg-white/20" />
              <div
                className="flex items-center gap-1.5 cursor-pointer rounded px-1.5 py-1 hover:bg-white/10 transition-colors"
                onClick={async () => {
                  try {
                    const result = await window.electronAPI.triggerScreenshot()
                    if (!result.success) {
                      console.error("Failed to take screenshot:", result.error)
                      showToast("Error", "Failed to take screenshot", "error")
                    }
                  } catch (error) {
                    console.error("Error taking screenshot:", error)
                    showToast("Error", "Failed to take screenshot", "error")
                  }
                }}
              >
                <span className="text-[11px] leading-none">Screenshot</span>
                <div className="flex gap-0.5">
                  <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                    {COMMAND_KEY}
                  </button>
                  <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                    H
                  </button>
                </div>
              </div>

              {extraScreenshots.length > 0 && (
                <>
                  <div className="mx-2 h-4 w-px bg-white/20" />
                  <div
                    className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                    onClick={async () => {
                      try {
                        const result = await window.electronAPI.triggerProcessScreenshots()
                        if (!result.success) {
                          console.error("Failed to process screenshots:", result.error)
                          showToast("Error", "Failed to process screenshots", "error")
                        }
                      } catch (error) {
                        console.error("Error processing screenshots:", error)
                        showToast("Error", "Failed to process screenshots", "error")
                      }
                    }}
                  >
                    <span className="text-[11px] leading-none">Debug</span>
                    <div className="flex gap-0.5">
                      <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                        {COMMAND_KEY}
                      </button>
                      <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                        ↵
                      </button>
                    </div>
                  </div>
                </>
              )}
            </>
          )}

          {/* Start Over - Always visible */}
          <div className="mx-1 h-4 w-px bg-white/20" />
          <div
            className="flex items-center gap-1.5 cursor-pointer rounded px-1.5 py-1 hover:bg-white/10 transition-colors"
            onClick={async () => {
              try {
                const result = await window.electronAPI.triggerReset()
                if (!result.success) {
                  console.error("Failed to reset:", result.error)
                  showToast("Error", "Failed to reset", "error")
                }
              } catch (error) {
                console.error("Error resetting:", error)
                showToast("Error", "Failed to reset", "error")
              }
            }}
          >
            <span className="text-[11px] leading-none">Start Over</span>
            <div className="flex gap-0.5">
              <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                {COMMAND_KEY}
              </button>
              <button className="bg-white/10 rounded-md px-1 py-0.5 text-[10px] leading-none text-white/70">
                R
              </button>
            </div>
          </div>

          {/* Separator */}
          <div className="mx-1 h-4 w-px bg-white/20" />

          {/* Settings */}
          <div
            className="flex items-center gap-1.5 cursor-pointer rounded px-1.5 py-1 hover:bg-white/10 transition-colors"
            onClick={handleSettingsClick}
          >
            <span className="text-[11px] leading-none">Settings</span>
            <div className="w-3.5 h-3.5 flex items-center justify-center text-white/70">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="w-3 h-3"
              >
                <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
                <circle cx="12" cy="12" r="3" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      <SettingsPanel
        isOpen={isSettingsOpen}
        currentLanguage={currentLanguage}
        setLanguage={setLanguage}
        onTakeScreenshot={handleTakeScreenshot}
        onSolve={handleSolve}
        onStartOver={handleStartOver}
        onToggleWindow={handleToggleWindow}
      />
    </div>
  )
}

export default SolutionCommands
