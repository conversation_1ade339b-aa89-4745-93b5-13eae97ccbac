# AI API 配置指南

## 概述

本项目支持通过用户界面配置AI API，无需手动编辑配置文件。支持官方 OpenAI API 和兼容 OpenAI 格式的自定义 API 端点。

## 配置步骤

### 1. 通过界面配置（推荐）

1. 启动应用程序
2. 点击设置按钮打开设置面板
3. 切换到"AI Settings"标签页
4. 配置以下信息：
   - **API URL**：API端点地址（如 https://api.openai.com/v1）
   - **API Key**：您的API密钥
   - **模型**：选择或输入模型名称
5. 点击"测试连接"验证配置
6. 连接成功后会自动获取可用模型列表

### 2. 环境变量配置（回退选项）

如果需要，仍可在项目根目录的 `.env` 文件中配置：

```env
# 这些配置仅作为回退选项，优先使用界面配置
# OPENAI_API_KEY=your_api_key_here
# OPENAI_BASE_URL=https://your-custom-api-endpoint.com/v1
# OPENAI_MODEL=gpt-4o
```

**注意：**
- 界面配置优先于环境变量
- 环境变量仅在用户未配置时作为回退选项

### 3. 支持的 API 提供商

本项目支持任何兼容 OpenAI 格式的 API，包括但不限于：
- **OpenAI 官方 API**：https://api.openai.com/v1
- **Azure OpenAI Service**：https://your-resource.openai.azure.com/
- **各种 OpenAI 代理服务**：如 https://api.example.com/v1
- **本地部署服务**：如 http://localhost:8000/v1
- **其他兼容服务**：Claude API（通过代理）、Gemini API（通过代理）等

### 4. 模型选择

- **自动获取**：连接成功后自动拉取可用模型列表
- **手动输入**：支持直接输入模型名称
- **常用模型**：gpt-4o, gpt-4o-mini, gpt-4-turbo, claude-3-sonnet等

常用模型示例：
- `gpt-4o` - GPT-4 Optimized（默认）
- `gpt-4` - GPT-4 标准版
- `gpt-3.5-turbo` - GPT-3.5 Turbo
- `claude-3-opus` - Claude 3 Opus（如果使用 Claude API）
- 其他兼容模型名称

修改模型只需编辑 `.env` 文件：
```env
OPENAI_MODEL=gpt-3.5-turbo
```

## 功能说明

### 支持的功能

1. **问题提取** (`extractProblem`)
   - 从截图中提取编程题目信息
   - 支持多张截图
   - 自动识别题目、示例、约束条件

2. **生成解决方案** (`generateSolution`)
   - 根据提取的问题生成代码解决方案
   - 包含详细的解题思路
   - 提供时间和空间复杂度分析

3. **代码调试** (`debugCode`)
   - 分析错误截图
   - 提供改进的代码方案
   - 解释修复内容

### 支持的编程语言

- Python
- JavaScript
- Java
- Go
- C#
- Rust
- C++
- Swift
- Kotlin
- Ruby

## 注意事项

1. **安全性**
   - 不要将 `.env` 文件提交到版本控制
   - 确保 `.env` 在 `.gitignore` 中
   - 不要在代码中硬编码 API 密钥

2. **API 限制**
   - 请注意您的 API 提供商的速率限制
   - 大图片可能会消耗更多 tokens
   - 建议使用清晰的截图以获得最佳效果

3. **错误处理**
   - 如果遇到 "API key not found" 错误，请在设置面板中配置API密钥
   - 如果遇到连接错误，请检查API URL是否正确
   - 使用"测试连接"功能验证配置
   - 查看 Electron 开发者工具的控制台获取详细错误信息

## 故障排除

### 常见问题

1. **API 密钥无效**
   - 在设置面板的AI设置中确认API密钥正确
   - 确认密钥有效且未过期
   - 使用"测试连接"功能验证

2. **无法连接到 API**
   - 检查API URL是否正确（确保以/v1结尾）
   - 确认网络连接正常
   - 检查是否需要代理设置
   - 尝试使用"测试连接"功能

3. **模型不支持**
   - 使用"刷新模型列表"获取可用模型
   - 确认您的 API 端点支持所选模型
   - 查看 API 提供商的文档了解支持的模型列表
   - 可以手动输入模型名称

## 开发调试

在开发模式下，您可以在 Electron 的开发者工具控制台中看到：
- API 初始化信息
- 请求和响应日志
- 错误详情

按 `Ctrl+Shift+I` (Windows/Linux) 或 `Cmd+Option+I` (Mac) 打开开发者工具。