import SubscribedApp from "./_pages/SubscribedApp"
import { UpdateNotification } from "./components/UpdateNotification"
import {
  QueryClient,
  QueryClientProvider,
} from "@tanstack/react-query"
import { useEffect, useState, useCallback } from "react"
import {
  Toast,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport
} from "./components/ui/toast"
import { ToastContext } from "./contexts/toast"

// Create a React Query client with optimized caching and memory management
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 2 * 60 * 1000, // 减少到2分钟
      gcTime: 5 * 60 * 1000, // 减少到5分钟后清理缓存
      retry: (failureCount, error) => {
        // 根据错误类型决定是否重试
        const errorMessage = error?.message || ''
        if (errorMessage.includes('API key') ||
            errorMessage.includes('quota') ||
            errorMessage.includes('cancelled')) {
          return false // 不重试这些错误
        }
        return failureCount < 1 // 减少重试次数到1次
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 10000), // 减少最大延迟
      refetchOnWindowFocus: false,
      refetchOnReconnect: false, // 减少不必要的重新获取
      networkMode: 'online' // 只在在线时执行查询
    },
    mutations: {
      retry: (failureCount, error) => {
        const errorMessage = error?.message || ''
        if (errorMessage.includes('API key') ||
            errorMessage.includes('quota') ||
            errorMessage.includes('cancelled')) {
          return false
        }
        return failureCount < 1 // mutations 最多重试1次
      },
      networkMode: 'online'
    }
  }
})



// Root component that provides the QueryClient
function App() {
  const [toastState, setToastState] = useState({
    open: false,
    title: "",
    description: "",
    variant: "neutral" as const
  })
  // No credits system - unlimited usage
  const [credits, setCredits] = useState<number>(999999)
  const [currentLanguage, setCurrentLanguage] = useState<string>("python")
  const [isInitialized, setIsInitialized] = useState(false)

  // Helper function to safely update language
  const updateLanguage = useCallback((newLanguage: string) => {
    setCurrentLanguage(newLanguage)
    window.__LANGUAGE__ = newLanguage
  }, [])

  // Helper function to mark initialization complete
  const markInitialized = useCallback(() => {
    setIsInitialized(true)
    window.__IS_INITIALIZED__ = true
  }, [])

  // Show toast method
  const showToast = useCallback(
    (
      title: string,
      description: string,
      variant: "neutral" | "success" | "error"
    ) => {
      setToastState({
        open: true,
        title,
        description,
        variant
      })
    },
    []
  )

  // Initialize app without authentication
  useEffect(() => {
    // Set unlimited credits
    window.__CREDITS__ = 999999
    window.__LANGUAGE__ = currentLanguage
    markInitialized()
  }, [currentLanguage, markInitialized])

  // 应用启动时自动加载模型列表（只执行一次）
  useEffect(() => {
    // 防止重复执行的标记
    if (window.__MODELS_LOADED_ON_STARTUP__) {
      console.log('App startup: Models already loaded, skipping...')
      return
    }

    const loadModelsOnStartup = async () => {
      try {
        // 立即设置标记，防止重复执行
        window.__MODELS_LOADED_ON_STARTUP__ = true

        // 获取AI配置
        const config = await window.electronAPI.getAIConfig()

        // 如果配置完整，则自动加载模型列表
        if (config.apiKey && config.apiUrl) {
          console.log('App startup: Auto-loading model list (one-time only)...')
          const result = await window.electronAPI.getAvailableModels()
          if (result.success && result.models) {
            console.log(`App startup: Successfully loaded ${result.models.length} models`)
          } else {
            console.log('App startup: Failed to load models:', result.error)
            // 如果失败，重置标记允许重试
            window.__MODELS_LOADED_ON_STARTUP__ = false
          }
        } else {
          console.log('App startup: API configuration incomplete, skipping model load')
          // 配置不完整时重置标记
          window.__MODELS_LOADED_ON_STARTUP__ = false
        }
      } catch (error) {
        console.error('App startup: Error loading models:', error)
        // 出错时重置标记允许重试
        window.__MODELS_LOADED_ON_STARTUP__ = false
      }
    }

    // 延迟一点时间确保服务已初始化
    const timer = setTimeout(loadModelsOnStartup, 2000)

    return () => clearTimeout(timer)
  }, [])

  return (
    <QueryClientProvider client={queryClient}>
      <ToastProvider>
        <ToastContext.Provider value={{ showToast }}>
          <AppContent 
            isInitialized={isInitialized} 
            credits={credits}
            currentLanguage={currentLanguage}
            setLanguage={updateLanguage}
          />
          <UpdateNotification />
          <Toast
            open={toastState.open}
            onOpenChange={(open) =>
              setToastState((prev) => ({ ...prev, open }))
            }
            variant={toastState.variant}
            duration={1500}
          >
            <ToastTitle>{toastState.title}</ToastTitle>
            <ToastDescription>{toastState.description}</ToastDescription>
          </Toast>
          <ToastViewport />
        </ToastContext.Provider>
      </ToastProvider>
    </QueryClientProvider>
  )
}

// Main App component - directly show SubscribedApp without auth
function AppContent({ 
  isInitialized, 
  credits, 
  currentLanguage, 
  setLanguage 
}: { 
  isInitialized: boolean
  credits: number
  currentLanguage: string
  setLanguage: (lang: string) => void
}) {
  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="flex flex-col items-center gap-3">
          <div className="w-6 h-6 border-2 border-white/20 border-t-white/80 rounded-full animate-spin"></div>
          <p className="text-white/60 text-sm">
            Initializing...
          </p>
        </div>
      </div>
    )
  }

  // Show the app directly without authentication
  return (
    <SubscribedApp
      credits={credits}
      currentLanguage={currentLanguage}
      setLanguage={setLanguage}
    />
  )
}

export default App