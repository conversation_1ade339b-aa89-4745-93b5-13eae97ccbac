// ScreenshotHelper.ts

import path from "node:path"
import fs from "node:fs"
import { app } from "electron"
import { v4 as uuidv4 } from "uuid"
import { execFile } from "child_process"
import { promisify } from "util"
import sharp from "sharp"
import crypto from "crypto"
import { CacheManager } from "./utils/CacheManager"

const execFileAsync = promisify(execFile)

// 图片优化配置
const IMAGE_OPTIMIZATION_CONFIG = {
  maxWidth: 1920,
  maxHeight: 1080,
  quality: 85,
  format: 'png' as const,
  // 最大文件大小 (bytes) - 约 2MB
  maxFileSize: 2 * 1024 * 1024
}

export class ScreenshotHelper {
  private screenshotQueue: string[] = []
  private extraScreenshotQueue: string[] = []
  private readonly MAX_SCREENSHOTS = 2

  private readonly screenshotDir: string
  private readonly extraScreenshotDir: string
  private readonly cacheDir: string

  private view: "queue" | "solutions" | "debug" = "queue"

  // 优化图片缓存管理器
  private optimizedImageCache: CacheManager<Buffer>

  constructor(view: "queue" | "solutions" | "debug" = "queue") {
    this.view = view

    // Initialize directories
    this.screenshotDir = path.join(app.getPath("userData"), "screenshots")
    this.extraScreenshotDir = path.join(
      app.getPath("userData"),
      "extra_screenshots"
    )
    this.cacheDir = path.join(app.getPath("userData"), "image_cache")

    // Create directories if they don't exist
    if (!fs.existsSync(this.screenshotDir)) {
      fs.mkdirSync(this.screenshotDir, { recursive: true })
    }
    if (!fs.existsSync(this.extraScreenshotDir)) {
      fs.mkdirSync(this.extraScreenshotDir, { recursive: true })
    }
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true })
    }

    // 初始化优化图片缓存管理器
    this.optimizedImageCache = new CacheManager<Buffer>('optimized_images', {
      maxAge: 2 * 60 * 60 * 1000,
      maxSize: 20,
      cleanupInterval: 30 * 60 * 1000
    })
  }

  public getView(): "queue" | "solutions" | "debug" {
    return this.view
  }

  public setView(view: "queue" | "solutions" | "debug"): void {
    console.log("Setting view in ScreenshotHelper:", view)
    console.log(
      "Current queues - Main:",
      this.screenshotQueue,
      "Extra:",
      this.extraScreenshotQueue
    )
    this.view = view
  }

  public getScreenshotQueue(): string[] {
    return this.screenshotQueue
  }

  public getExtraScreenshotQueue(): string[] {
    console.log("Getting extra screenshot queue:", this.extraScreenshotQueue)
    return this.extraScreenshotQueue
  }

  public clearQueues(): void {
    // Clear screenshotQueue
    this.screenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(`Error deleting screenshot at ${screenshotPath}:`, err)
      })
    })
    this.screenshotQueue = []

    // Clear extraScreenshotQueue
    this.extraScreenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(
            `Error deleting extra screenshot at ${screenshotPath}:`,
            err
          )
      })
    })
    this.extraScreenshotQueue = []
  }

  /**
   * 生成图片内容的哈希值用于缓存
   */
  private generateImageHash(buffer: Buffer): string {
    return crypto.createHash('sha256').update(buffer).digest('hex').substring(0, 16)
  }

  /**
   * 销毁截图助手并清理资源
   */
  public destroy(): void {
    this.optimizedImageCache.destroy()
  }

  /**
   * 优化图片：压缩、调整尺寸、格式转换
   */
  private async optimizeImage(buffer: Buffer): Promise<Buffer> {
    try {
      const image = sharp(buffer)
      const metadata = await image.metadata()

      console.log(`Original image: ${metadata.width}x${metadata.height}, ${buffer.length} bytes`)

      let optimized = image
        .png({ quality: IMAGE_OPTIMIZATION_CONFIG.quality, compressionLevel: 9 })

      // 如果图片太大，调整尺寸
      if (metadata.width && metadata.height) {
        const { maxWidth, maxHeight } = IMAGE_OPTIMIZATION_CONFIG
        if (metadata.width > maxWidth || metadata.height > maxHeight) {
          optimized = optimized.resize(maxWidth, maxHeight, {
            fit: 'inside',
            withoutEnlargement: true
          })
        }
      }

      const optimizedBuffer = await optimized.toBuffer()
      console.log(`Optimized image: ${optimizedBuffer.length} bytes (${((1 - optimizedBuffer.length / buffer.length) * 100).toFixed(1)}% reduction)`)

      // 如果优化后的文件仍然太大，进一步压缩
      if (optimizedBuffer.length > IMAGE_OPTIMIZATION_CONFIG.maxFileSize) {
        console.log('Image still too large, applying additional compression...')
        const furtherOptimized = await sharp(optimizedBuffer)
          .png({ quality: 70, compressionLevel: 9 })
          .toBuffer()

        console.log(`Further optimized: ${furtherOptimized.length} bytes`)
        return furtherOptimized
      }

      return optimizedBuffer
    } catch (error) {
      console.error('Error optimizing image:', error)
      // 如果优化失败，返回原始图片
      return buffer
    }
  }

  private async captureScreenshotMac(): Promise<Buffer> {
    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`)
    await execFileAsync("screencapture", ["-x", tmpPath])
    const buffer = await fs.promises.readFile(tmpPath)
    await fs.promises.unlink(tmpPath)
    return buffer
  }

  private async captureScreenshotWindows(): Promise<Buffer> {
    // Using PowerShell's native screenshot capability
    const tmpPath = path.join(app.getPath("temp"), `${uuidv4()}.png`)
    const script = `
      Add-Type -AssemblyName System.Windows.Forms
      Add-Type -AssemblyName System.Drawing
      $screen = [System.Windows.Forms.Screen]::PrimaryScreen
      $bitmap = New-Object System.Drawing.Bitmap $screen.Bounds.Width, $screen.Bounds.Height
      $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
      $graphics.CopyFromScreen($screen.Bounds.X, $screen.Bounds.Y, 0, 0, $bitmap.Size)
      $bitmap.Save('${tmpPath.replace(/\\/g, "\\\\")}')
      $graphics.Dispose()
      $bitmap.Dispose()
    `
    await execFileAsync("powershell", ["-command", script])
    const buffer = await fs.promises.readFile(tmpPath)
    await fs.promises.unlink(tmpPath)
    return buffer
  }

  public async takeScreenshot(
    hideMainWindow: () => void,
    showMainWindow: () => void
  ): Promise<string> {
    console.log("Taking screenshot in view:", this.view)
    hideMainWindow()
    await new Promise((resolve) => setTimeout(resolve, 100))

    let screenshotPath = ""
    try {
      // Get screenshot buffer using native methods
      const originalBuffer =
        process.platform === "darwin"
          ? await this.captureScreenshotMac()
          : await this.captureScreenshotWindows()

      // 生成图片哈希用于缓存检查
      const imageHash = this.generateImageHash(originalBuffer)

      // 检查缓存中是否已有优化版本
      let optimizedBuffer: Buffer
      const cachedBuffer = this.optimizedImageCache.get(imageHash)

      if (cachedBuffer) {
        console.log("Using cached optimized image:", imageHash)
        optimizedBuffer = cachedBuffer
      } else {
        console.log("Optimizing new image:", imageHash)
        optimizedBuffer = await this.optimizeImage(originalBuffer)
        // 保存到缓存
        this.optimizedImageCache.set(imageHash, optimizedBuffer)
      }

      // Save and manage the screenshot based on current view
      if (this.view === "queue") {
        screenshotPath = path.join(this.screenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, optimizedBuffer)
        console.log("Adding optimized screenshot to main queue:", screenshotPath)
        this.screenshotQueue.push(screenshotPath)
        if (this.screenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.screenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
              console.log(
                "Removed old screenshot from main queue:",
                removedPath
              )
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      } else {
        // In solutions view, only add to extra queue
        screenshotPath = path.join(this.extraScreenshotDir, `${uuidv4()}.png`)
        await fs.promises.writeFile(screenshotPath, optimizedBuffer)
        console.log("Adding optimized screenshot to extra queue:", screenshotPath)
        this.extraScreenshotQueue.push(screenshotPath)
        if (this.extraScreenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.extraScreenshotQueue.shift()
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath)
              console.log(
                "Removed old screenshot from extra queue:",
                removedPath
              )
            } catch (error) {
              console.error("Error removing old screenshot:", error)
            }
          }
        }
      }
    } catch (error) {
      console.error("Screenshot error:", error)
      throw error
    } finally {
      await new Promise((resolve) => setTimeout(resolve, 50))
      showMainWindow()
    }

    return screenshotPath
  }

  public async getImagePreview(filepath: string): Promise<string> {
    try {
      const data = await fs.promises.readFile(filepath)
      return `data:image/png;base64,${data.toString("base64")}`
    } catch (error) {
      console.error("Error reading image:", error)
      throw error
    }
  }

  public async deleteScreenshot(
    path: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      await fs.promises.unlink(path)
      if (this.view === "queue") {
        this.screenshotQueue = this.screenshotQueue.filter(
          (filePath) => filePath !== path
        )
      } else {
        this.extraScreenshotQueue = this.extraScreenshotQueue.filter(
          (filePath) => filePath !== path
        )
      }
      return { success: true }
    } catch (error) {
      console.error("Error deleting file:", error)
      return { success: false, error: error.message }
    }
  }

  public clearExtraScreenshotQueue(): void {
    // Clear extraScreenshotQueue
    this.extraScreenshotQueue.forEach((screenshotPath) => {
      fs.unlink(screenshotPath, (err) => {
        if (err)
          console.error(
            `Error deleting extra screenshot at ${screenshotPath}:`,
            err
          )
      })
    })
    this.extraScreenshotQueue = []
  }
}
