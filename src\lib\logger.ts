// Simple logger utility for frontend that only logs in development mode
const isDev = import.meta.env.DEV

export const logger = {
  log: (...args: any[]) => {
    if (isDev) {
      console.log('[App]', ...args)
    }
  },
  
  error: (...args: any[]) => {
    // Always log errors
    console.error('[App Error]', ...args)
  },
  
  warn: (...args: any[]) => {
    if (isDev) {
      console.warn('[App Warning]', ...args)
    }
  },
  
  debug: (...args: any[]) => {
    if (isDev && import.meta.env.VITE_DEBUG) {
      console.debug('[App Debug]', ...args)
    }
  }
}