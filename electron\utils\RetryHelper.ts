// RetryHelper.ts - 智能重试机制

import { ErrorHandler } from './ErrorHandler'

export interface RetryConfig {
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  timeoutMs: number
  backoffMultiplier?: number
  jitterMs?: number
  retryCondition?: (error: any, attempt: number) => boolean
}

export class RetryHelper {
  private static defaultConfig: RetryConfig = {
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    timeoutMs: 60000, // 60秒超时
    backoffMultiplier: 2,
    jitterMs: 500,
    retryCondition: (error: any, attempt: number) => {
      // 默认重试条件：网络错误、超时、速率限制、连接错误
      const errorMessage = error?.message?.toLowerCase() || ''
      const errorCode = error?.code || error?.status

      // 网络相关错误
      const networkErrors = [
        'network', 'timeout', 'rate limit', 'connection', 'econnreset',
        'enotfound', 'econnrefused', 'etimedout', 'socket hang up',
        'no response', 'empty response', 'request failed'
      ]

      // HTTP状态码错误
      const retryableStatusCodes = [429, 500, 502, 503, 504, 520, 521, 522, 523, 524]

      // 检查错误消息
      const hasNetworkError = networkErrors.some(keyword =>
        errorMessage.includes(keyword)
      )

      // 检查状态码
      const hasRetryableStatus = retryableStatusCodes.includes(errorCode)

      return hasNetworkError || hasRetryableStatus
    }
  }

  /**
   * 带重试的异步函数执行
   */
  static async executeWithRetry<T>(
    fn: () => Promise<T>,
    config: Partial<RetryConfig> = {},
    context?: string
  ): Promise<T> {
    const finalConfig = { ...this.defaultConfig, ...config }
    let lastError: any

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        console.log(`${context ? `[${context}] ` : ''}Attempt ${attempt}/${finalConfig.maxAttempts}`)
        
        // 创建超时 Promise
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => {
            reject(new Error(`Operation timed out after ${finalConfig.timeoutMs}ms`))
          }, finalConfig.timeoutMs)
        })

        // 执行函数并应用超时
        const result = await Promise.race([fn(), timeoutPromise])
        
        if (attempt > 1) {
          console.log(`${context ? `[${context}] ` : ''}Success on attempt ${attempt}`)
        }
        
        return result
      } catch (error) {
        lastError = error
        const analyzedError = ErrorHandler.analyzeError(error)
        
        ErrorHandler.logError(analyzedError, context)

        // 检查自定义重试条件
        const shouldRetry = finalConfig.retryCondition ?
          finalConfig.retryCondition(error, attempt) :
          analyzedError.retryable

        // 如果是最后一次尝试，或者错误不可重试，直接抛出
        if (attempt === finalConfig.maxAttempts || !shouldRetry) {
          console.error(`${context ? `[${context}] ` : ''}Failed after ${attempt} attempts`)
          throw error
        }

        // 计算智能延迟时间（指数退避 + 抖动）
        const baseDelay = finalConfig.baseDelay * Math.pow(finalConfig.backoffMultiplier || 2, attempt - 1)
        const jitter = Math.random() * (finalConfig.jitterMs || 0)
        const delay = Math.min(baseDelay + jitter, finalConfig.maxDelay)

        console.log(`${context ? `[${context}] ` : ''}Retrying in ${Math.round(delay)}ms... (attempt ${attempt + 1}/${finalConfig.maxAttempts})`)
        
        // 等待后重试
        await this.sleep(delay)
      }
    }

    throw lastError
  }

  /**
   * 带重试的 OpenAI API 调用
   */
  static async executeOpenAICall<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<T> {
    return this.executeWithRetry(
      fn,
      {
        maxAttempts: 3,
        timeoutMs: 90000 // OpenAI 调用给更长的超时时间
      },
      context
    )
  }

  /**
   * 带重试的图片处理
   */
  static async executeImageProcessing<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<T> {
    return this.executeWithRetry(
      fn,
      {
        maxAttempts: 2,
        timeoutMs: 30000 // 图片处理超时时间较短
      },
      context
    )
  }

  /**
   * 睡眠函数
   */
  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 创建可取消的 Promise
   */
  static createCancellablePromise<T>(
    fn: () => Promise<T>,
    signal?: AbortSignal
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      // 检查是否已经取消
      if (signal?.aborted) {
        reject(new Error('Operation was cancelled'))
        return
      }

      // 监听取消信号
      const onAbort = () => {
        reject(new Error('Operation was cancelled'))
      }

      signal?.addEventListener('abort', onAbort)

      // 执行函数
      fn()
        .then(resolve)
        .catch(reject)
        .finally(() => {
          signal?.removeEventListener('abort', onAbort)
        })
    })
  }

  /**
   * 带取消支持的重试执行
   */
  static async executeWithRetryAndCancel<T>(
    fn: () => Promise<T>,
    signal?: AbortSignal,
    config: Partial<RetryConfig> = {},
    context?: string
  ): Promise<T> {
    const finalConfig = { ...this.defaultConfig, ...config }
    let lastError: any

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        // 检查是否已取消
        if (signal?.aborted) {
          throw new Error('Operation was cancelled by user')
        }

        console.log(`${context ? `[${context}] ` : ''}Attempt ${attempt}/${finalConfig.maxAttempts}`)
        
        // 创建可取消的执行
        const result = await this.createCancellablePromise(fn, signal)
        
        if (attempt > 1) {
          console.log(`${context ? `[${context}] ` : ''}Success on attempt ${attempt}`)
        }
        
        return result
      } catch (error) {
        // 如果是用户取消，直接抛出
        if (signal?.aborted || (error as Error).message.includes('cancelled')) {
          throw error
        }

        lastError = error
        const analyzedError = ErrorHandler.analyzeError(error)
        
        ErrorHandler.logError(analyzedError, context)

        // 如果是最后一次尝试，或者错误不可重试，直接抛出
        if (attempt === finalConfig.maxAttempts || !analyzedError.retryable) {
          console.error(`${context ? `[${context}] ` : ''}Failed after ${attempt} attempts`)
          throw error
        }

        // 计算延迟时间
        const delay = ErrorHandler.getRetryDelay(attempt - 1, analyzedError.type)
        console.log(`${context ? `[${context}] ` : ''}Retrying in ${delay}ms...`)
        
        // 可取消的等待
        await this.createCancellablePromise(
          () => this.sleep(delay),
          signal
        )
      }
    }

    throw lastError
  }
}
