import OpenAI from 'openai'
// import { <PERSON>rror<PERSON>and<PERSON> } from '../utils/ErrorHandler' // 暂时未使用
import { RetryHelper } from '../utils/RetryHelper'
import { cacheService } from './CacheService'
import { performanceMonitor } from './PerformanceMonitor'
import { aiConfigService } from './AIConfigService'

// Language mapping for better prompt engineering
const LANGUAGE_MAPPING: Record<string, string> = {
  'python': 'Python',
  'javascript': 'JavaScript',
  'java': 'Java',
  'golang': 'Go',
  'csharp': 'C#',
  'rust': 'Rust',
  'cpp': 'C++',
  'swift': 'Swift',
  'kotlin': 'Kotlin',
  'ruby': 'Ruby'
}

export interface ProblemInfo {
  title?: string
  description: string
  examples?: Array<{
    input: string
    output: string
    explanation?: string
  }>
  constraints?: string[]
}

export interface SolutionResponse {
  code: string
  thoughts: string[]
  time_complexity: string
  space_complexity: string
}

export interface DebugResponse {
  new_code: string
  thoughts: string[]
  time_complexity: string
  space_complexity: string
}

export class OpenAIService {
  private static instance: OpenAIService | null = null
  private openai: OpenAI | null = null
  private modelName: string
  private initialized: boolean = false
  private cachedModels: string[] | null = null
  private lastModelsFetch: number = 0
  private readonly MODELS_CACHE_DURATION = 60 * 60 * 1000 // 1小时缓存

  private constructor() {
    // 私有构造函数，防止直接实例化
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService()
      OpenAIService.instance.initialize()
    }
    return OpenAIService.instance
  }

  /**
   * 初始化服务
   */
  private initialize(): void {
    if (this.initialized) {
      return
    }

    console.log('[OpenAI Service] Initializing service...')
    this.initializeOpenAI()
    this.updateModelName()
    this.initialized = true
    console.log('[OpenAI Service] Service initialized successfully')
  }



  /**
   * 更新模型名称（优先使用用户配置）
   */
  private updateModelName() {
    try {
      // 从用户配置获取模型
      const userConfig = aiConfigService.getConfig()

      if (userConfig.model) {
        this.modelName = userConfig.model
        console.log(`[OpenAI Service] Using user configured model: ${this.modelName}`)
      } else {
        this.modelName = 'gpt-4o'
        console.log(`[OpenAI Service] Using default model: ${this.modelName}`)
      }
    } catch (error) {
      console.error('[OpenAI Service] Error updating model name:', error)
      this.modelName = 'gpt-4o'
    }
  }

  private initializeOpenAI() {
    try {
      // 从用户配置获取API信息
      const userConfig = aiConfigService.getConfig()

      const apiKey = userConfig.apiKey
      let baseURL = userConfig.apiUrl

      // 如果baseURL是默认的OpenAI URL，则设为undefined让SDK使用默认值
      if (baseURL === 'https://api.openai.com/v1') {
        baseURL = undefined
      }

      console.log(`[OpenAI Service] Using API Key: ${apiKey ? '***CONFIGURED***' : 'NOT FOUND'}`)
      console.log(`[OpenAI Service] Using Base URL: ${baseURL || 'default (api.openai.com)'}`)

      if (!apiKey) {
        console.error('[OpenAI Service] OpenAI API key not found in user config')
        return
      }

      this.openai = new OpenAI({
        apiKey: apiKey,
        baseURL: baseURL
      })

      console.log('[OpenAI Service] OpenAI client initialized successfully')
    } catch (error) {
      console.error('[OpenAI Service] Failed to initialize OpenAI client:', error)
    }
  }

  /**
   * 清除模型缓存
   */
  public clearModelsCache(): void {
    this.cachedModels = null
    this.lastModelsFetch = 0
    console.log('[OpenAI Service] Models cache cleared')
  }

  /**
   * 重新初始化OpenAI客户端（配置更新后调用）
   */
  reinitialize(): void {
    console.log('[OpenAI Service] Reinitializing with new configuration...')
    // 清除缓存，因为配置可能已更改
    this.clearModelsCache()
    this.initializeOpenAI()
    this.updateModelName()
    console.log('[OpenAI Service] Reinitialization completed')
  }

  /**
   * 测试API连接
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.openai) {
        // 尝试重新初始化
        this.initializeOpenAI()
        if (!this.openai) {
          return { success: false, error: 'API client not initialized. Please check your API key and URL.' }
        }
      }

      // 发送一个简单的测试请求
      const response = await RetryHelper.executeWithRetry(async () => {
        console.log('[OpenAI API] Sending connection test request...')
        return await this.openai!.chat.completions.create({
          model: aiConfigService.getConfig().model || 'gpt-4o-mini',
          messages: [
            { role: 'user', content: 'Hello, this is a connection test. Please respond with "OK".' }
          ],
          max_tokens: 10,
          temperature: 0
        })
      }, {
        maxAttempts: 2,
        baseDelay: 1000,
        maxDelay: 5000,
        timeoutMs: 30000
      }, 'Connection Test')

      if (response.choices && response.choices.length > 0) {
        return { success: true }
      } else {
        return { success: false, error: 'No response received from API' }
      }
    } catch (error: any) {
      console.error('[OpenAI Service] Connection test failed:', error)
      return {
        success: false,
        error: error.message || 'Connection test failed'
      }
    }
  }

  /**
   * 解析API响应中的JSON内容
   */
  private parseJSONResponse(content: string): SolutionResponse {
    let jsonContent = content.trim()

    console.log('Parsing JSON response, length:', jsonContent.length)

    // 1. 首先尝试匹配 markdown 代码块
    const codeBlockMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
    if (codeBlockMatch) {
      jsonContent = codeBlockMatch[1].trim()
      console.log('[JSON Parser] Extracted from code block')
    }

    // 2. 如果不是以 { 开头，尝试找到第一个完整的 JSON 对象
    if (!jsonContent.startsWith('{')) {
      const firstBrace = jsonContent.indexOf('{')
      if (firstBrace !== -1) {
        jsonContent = jsonContent.substring(firstBrace)
        console.log('[JSON Parser] Found JSON start at position:', firstBrace)
      }
    }

    // 3. 验证JSON是否完整 - 检查括号平衡
    const braceBalance = this.checkBraceBalance(jsonContent)
    if (braceBalance !== 0) {
      console.error('[JSON Parser] Unbalanced braces, balance:', braceBalance)

      // 尝试修复截断的JSON
      if (braceBalance > 0) {
        // 缺少右括号，尝试添加
        const missingBraces = '}}'.repeat(braceBalance)
        jsonContent += missingBraces
        console.log('[JSON Parser] Added missing closing braces:', missingBraces)
      }
    }

    // 4. 清理末尾的非JSON字符
    const lastBrace = jsonContent.lastIndexOf('}')
    if (lastBrace !== -1 && lastBrace < jsonContent.length - 1) {
      jsonContent = jsonContent.substring(0, lastBrace + 1)
      console.log('[JSON Parser] Trimmed content after last brace')
    }

    console.log('[JSON Parser] Final content length:', jsonContent.length)
    console.log('[JSON Parser] Final content preview:', jsonContent.substring(0, 300) + '...')

    try {
      const parsed = JSON.parse(jsonContent) as SolutionResponse
      console.log('[JSON Parser] Successfully parsed JSON')
      return parsed
    } catch (parseError) {
      console.error('[JSON Parser] Failed to parse JSON:', parseError)
      console.error('[JSON Parser] Content that failed to parse:', jsonContent)

      // 尝试部分恢复
      return this.attemptPartialRecovery(jsonContent)
    }
  }

  /**
   * 检查括号平衡
   */
  private checkBraceBalance(content: string): number {
    let balance = 0
    let inString = false
    let escaped = false

    for (let i = 0; i < content.length; i++) {
      const char = content[i]

      if (escaped) {
        escaped = false
        continue
      }

      if (char === '\\') {
        escaped = true
        continue
      }

      if (char === '"') {
        inString = !inString
        continue
      }

      if (!inString) {
        if (char === '{') {
          balance++
        } else if (char === '}') {
          balance--
        }
      }
    }

    return balance
  }

  /**
   * 尝试部分恢复损坏的JSON
   */
  private attemptPartialRecovery(content: string): SolutionResponse {
    console.log('[JSON Parser] Attempting partial recovery')

    // 尝试提取基本字段
    const codeMatch = content.match(/"code"\s*:\s*"([^"]*(?:\\.[^"]*)*)"/)
    const thoughtsMatch = content.match(/"thoughts"\s*:\s*\[([\s\S]*?)\]/)
    const timeComplexityMatch = content.match(/"time_complexity"\s*:\s*"([^"]*)"/)
    const spaceComplexityMatch = content.match(/"space_complexity"\s*:\s*"([^"]*)"/)

    const recovery: SolutionResponse = {
      code: codeMatch ? codeMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n') : '// 解决方案代码被截断',
      thoughts: thoughtsMatch ? this.parseThoughtsArray(thoughtsMatch[1]) : ['分析内容被截断'],
      time_complexity: timeComplexityMatch ? timeComplexityMatch[1] : 'O(n) - 分析被截断',
      space_complexity: spaceComplexityMatch ? spaceComplexityMatch[1] : 'O(1) - 分析被截断'
    }

    console.log('[JSON Parser] Partial recovery completed')
    return recovery
  }

  /**
   * 解析thoughts数组
   */
  private parseThoughtsArray(thoughtsContent: string): string[] {
    try {
      const thoughts: string[] = []
      const matches = thoughtsContent.match(/"([^"]*(?:\\.[^"]*)*)"/g)
      if (matches) {
        for (const match of matches) {
          const thought = match.slice(1, -1).replace(/\\"/g, '"').replace(/\\n/g, '\n')
          thoughts.push(thought)
        }
      }
      return thoughts.length > 0 ? thoughts : ['分析内容被截断']
    } catch {
      return ['分析内容被截断']
    }
  }

  /**
   * 解析问题提取的JSON响应
   */
  private parseProblemJSONResponse(content: string): ProblemInfo {
    let jsonContent = content.trim()

    console.log('[JSON Parser] Parsing problem JSON, length:', jsonContent.length)

    // 1. 首先尝试匹配 markdown 代码块
    const codeBlockMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
    if (codeBlockMatch) {
      jsonContent = codeBlockMatch[1].trim()
      console.log('[JSON Parser] Extracted from code block')
    }

    // 2. 如果不是以 { 开头，尝试找到第一个完整的 JSON 对象
    if (!jsonContent.startsWith('{')) {
      const firstBrace = jsonContent.indexOf('{')
      if (firstBrace !== -1) {
        jsonContent = jsonContent.substring(firstBrace)
        console.log('[JSON Parser] Found JSON start at position:', firstBrace)
      }
    }

    // 3. 验证JSON是否完整 - 检查括号平衡
    const braceBalance = this.checkBraceBalance(jsonContent)
    if (braceBalance !== 0) {
      console.error('[JSON Parser] Unbalanced braces, balance:', braceBalance)

      // 尝试修复截断的JSON
      if (braceBalance > 0) {
        // 缺少右括号，尝试添加
        const missingBraces = '}}'.repeat(braceBalance)
        jsonContent += missingBraces
        console.log('[JSON Parser] Added missing closing braces:', missingBraces)
      }
    }

    // 4. 清理末尾的非JSON字符
    const lastBrace = jsonContent.lastIndexOf('}')
    if (lastBrace !== -1 && lastBrace < jsonContent.length - 1) {
      jsonContent = jsonContent.substring(0, lastBrace + 1)
      console.log('[JSON Parser] Trimmed content after last brace')
    }

    console.log('[JSON Parser] Final problem content length:', jsonContent.length)
    console.log('[JSON Parser] Final problem content preview:', jsonContent.substring(0, 300) + '...')

    try {
      const parsed = JSON.parse(jsonContent) as ProblemInfo
      console.log('[JSON Parser] Successfully parsed problem JSON')
      return parsed
    } catch (parseError) {
      console.error('[JSON Parser] Failed to parse problem JSON:', parseError)
      console.error('[JSON Parser] Content that failed to parse:', jsonContent.substring(0, 500) + '...')

      // 尝试部分恢复
      return this.attemptProblemRecovery(jsonContent)
    }
  }

  /**
   * 尝试部分恢复问题信息
   */
  private attemptProblemRecovery(content: string): ProblemInfo {
    console.log('[JSON Parser] Attempting problem recovery')

    // 尝试提取基本字段
    const titleMatch = content.match(/"title"\s*:\s*"([^"]*)"/)
    const descriptionMatch = content.match(/"description"\s*:\s*"([^"]*(?:\\.[^"]*)*)"/)

    const recovery: ProblemInfo = {
      title: titleMatch ? titleMatch[1] : 'Problem title was truncated',
      description: descriptionMatch ? descriptionMatch[1].replace(/\\"/g, '"').replace(/\\n/g, '\n') : 'Problem description was truncated',
      examples: [],
      constraints: []
    }

    // 尝试提取示例
    const examplesMatch = content.match(/"examples"\s*:\s*\[([\s\S]*?)\]/)
    if (examplesMatch) {
      try {
        const examplesArray = JSON.parse(`[${examplesMatch[1]}]`)
        recovery.examples = examplesArray
      } catch {
        recovery.examples = [{ input: 'Example was truncated', output: 'Example was truncated' }]
      }
    }

    // 尝试提取约束
    const constraintsMatch = content.match(/"constraints"\s*:\s*\[([\s\S]*?)\]/)
    if (constraintsMatch) {
      try {
        const constraintsArray = JSON.parse(`[${constraintsMatch[1]}]`)
        recovery.constraints = constraintsArray
      } catch {
        recovery.constraints = ['Constraints were truncated']
      }
    }

    console.log('[JSON Parser] Problem recovery completed')
    return recovery
  }

  /**
   * 检查API健康状态
   */
  async checkAPIHealth(): Promise<{ healthy: boolean; details: any }> {
    try {
      if (!this.openai) {
        return {
          healthy: false,
          details: { error: 'OpenAI client not initialized' }
        }
      }

      const startTime = Date.now()
      const testResult = await this.testConnection()
      const responseTime = Date.now() - startTime

      return {
        healthy: testResult.success,
        details: {
          responseTime,
          model: this.modelName,
          apiUrl: aiConfigService.getConfig().apiUrl,
          hasApiKey: !!aiConfigService.getConfig().apiKey,
          error: testResult.error
        }
      }
    } catch (error: any) {
      return {
        healthy: false,
        details: {
          error: error.message,
          stack: error.stack
        }
      }
    }
  }

  /**
   * 获取可用的模型列表
   */
  async getAvailableModels(): Promise<{ success: boolean; models?: string[]; error?: string }> {
    try {
      const now = Date.now()

      // 检查缓存是否有效
      if (this.cachedModels &&
          this.cachedModels.length > 0 &&
          (now - this.lastModelsFetch) < this.MODELS_CACHE_DURATION) {
        console.log(`[OpenAI Service] Using cached models (${this.cachedModels.length} models, cached ${Math.floor((now - this.lastModelsFetch) / 60000)}min ago)`)
        return { success: true, models: this.cachedModels }
      }

      // 确保 OpenAI 客户端已初始化
      if (!this.openai) {
        console.log('[OpenAI Service] Initializing OpenAI client for model fetching...')
        this.initializeOpenAI()
        if (!this.openai) {
          return { success: false, error: 'API client not initialized. Please check your API key and URL.' }
        }
      }

      console.log('[OpenAI Service] Fetching available models from API...')

      // 设置超时时间
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Request timeout')), 10000) // 10秒超时
      })

      const response = await Promise.race([
        this.openai.models.list(),
        timeoutPromise
      ])

      if (response.data && Array.isArray(response.data)) {
        // 过滤出聊天模型
        const chatModels = response.data
          .filter(model => {
            const id = model.id.toLowerCase()
            return (
              // 包含常见的聊天模型标识
              id.includes('gpt') ||
              id.includes('claude') ||
              id.includes('gemini') ||
              id.includes('llama') ||
              id.includes('mistral') ||
              id.includes('qwen') ||
              id.includes('yi-') ||
              id.includes('baichuan') ||
              id.includes('chatglm') ||
              // 排除明显不是聊天模型的
              (!id.includes('whisper') &&
               !id.includes('tts') &&
               !id.includes('text-to-speech') &&
               !id.includes('dall-e') &&
               !id.includes('embedding') &&
               !id.includes('moderation') &&
               !id.includes('edit') &&
               !id.includes('code-search') &&
               !id.includes('similarity'))
            )
          })
          .map(model => model.id)
          .sort((a, b) => {
            // 优先显示常用模型
            const priority = ['gpt-4o', 'gpt-4o-mini', 'gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo']
            const aIndex = priority.indexOf(a)
            const bIndex = priority.indexOf(b)

            if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex
            if (aIndex !== -1) return -1
            if (bIndex !== -1) return 1
            return a.localeCompare(b)
          })

        console.log(`[OpenAI Service] Found ${chatModels.length} chat models:`, chatModels.slice(0, 5))

        // 缓存结果
        this.cachedModels = chatModels
        this.lastModelsFetch = Date.now()
        console.log('[OpenAI Service] Models cached for 1 hour')

        return { success: true, models: chatModels }
      } else {
        return { success: false, error: 'Invalid response format from API' }
      }
    } catch (error: any) {
      console.error('[OpenAI Service] Failed to fetch models:', error)

      // 如果API不支持模型列表或出现特定错误，返回常用模型
      if (error.status === 404 ||
          error.message?.includes('not found') ||
          error.message?.includes('timeout') ||
          error.message?.includes('models') === false) {
        console.log('[OpenAI Service] API does not support model listing or timed out, returning common models')
        const defaultModels = [
          'gpt-4o',
          'gpt-4o-mini',
          'gpt-4-turbo',
          'gpt-4',
          'gpt-3.5-turbo',
          'claude-3-5-sonnet',
          'claude-3-sonnet',
          'claude-3-haiku',
          'claude-3-opus',
          'gemini-pro',
          'llama-2-70b-chat'
        ]

        // 缓存默认模型列表
        this.cachedModels = defaultModels
        this.lastModelsFetch = Date.now()
        console.log('[OpenAI Service] Default models cached for 1 hour')

        return {
          success: true,
          models: defaultModels
        }
      }

      return {
        success: false,
        error: error.message || 'Failed to fetch models'
      }
    }
  }

  async extractProblem(imageDataList: string[], language: string): Promise<ProblemInfo> {
    const operationId = performanceMonitor.startOperation('problem_extraction', {
      imageCount: imageDataList.length,
      language,
      model: this.modelName
    })

    try {
      if (!this.openai) {
        throw new Error('OpenAI client not initialized. Please check your API key.')
      }



      // 检查缓存
      const cacheKey = { imageDataList, language, model: this.modelName }
      const cachedResult = await cacheService.get<ProblemInfo>('problem_extraction', cacheKey)
      if (cachedResult) {
        console.log('[OpenAI] Using cached problem extraction result')
        performanceMonitor.recordCacheHit('problem_extraction')
        performanceMonitor.endOperation(operationId, true)
        return cachedResult
      }

    return RetryHelper.executeOpenAICall(async () => {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `提取编程题。JSON格式。`
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `提取题目信息。JSON：{"title":"","description":"","examples":[],"constraints":[]}`
            },
            ...imageDataList.map(imageData => ({
              type: 'image_url' as const,
              image_url: {
                url: `data:image/png;base64,${imageData}`,
                detail: 'high' as const
              }
            }))
          ]
        }
      ]

      // 获取用户配置
      const userConfig = aiConfigService.getConfig()
      const maxTokens = userConfig.maxTokens?.problemExtraction || 1000 // 1K tokens for max speed

      const response = await RetryHelper.executeWithRetry(async () => {
        console.log('[OpenAI API] Sending problem extraction request...', {
          model: this.modelName,
          maxTokens,
          messagesCount: messages.length
        })

        try {
          return await this.openai!.chat.completions.create({
            model: this.modelName,
            messages,
            max_tokens: maxTokens,
            temperature: userConfig.temperature || 0.0
          }, {
            timeout: userConfig.timeout?.problemExtraction || 90000 // 增加超时
          })
        } catch (apiError: any) {
          console.error('[OpenAI API] Problem extraction request failed:', {
            error: apiError.message,
            status: apiError.status,
            code: apiError.code,
            type: apiError.type
          })
          throw apiError
        }
      }, {
        maxAttempts: 1,  // 只尝试1次，最大速度
        baseDelay: 500,
        maxDelay: 2000,
        timeoutMs: userConfig.timeout?.problemExtraction || 20000
      }, 'Problem Extraction')



      const content = response.choices[0]?.message?.content
      if (!content) {
        const errorDetails = {
          hasChoices: !!response.choices?.length,
          choicesCount: response.choices?.length || 0,
          hasMessage: !!response.choices?.[0]?.message,
          finishReason: response.choices?.[0]?.finish_reason,
          usage: response.usage
        }
        console.error('[OpenAI API] No content in response:', errorDetails)
        throw new Error(`No response content from OpenAI API. Details: ${JSON.stringify(errorDetails)}`)
      }

      console.log('Raw API response:', content)

      // 更强健的 JSON 提取逻辑
      let jsonContent = content.trim()

      // 1. 首先尝试匹配 markdown 代码块
      const codeBlockMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      if (codeBlockMatch) {
        jsonContent = codeBlockMatch[1].trim()
        console.log('Extracted from code block:', jsonContent.substring(0, 100) + '...')
      }

      // 2. 如果仍然不是以 { 开头，尝试找到第一个完整的 JSON 对象
      if (!jsonContent.startsWith('{')) {
        const firstBrace = jsonContent.indexOf('{')
        const lastBrace = jsonContent.lastIndexOf('}')
        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
          jsonContent = jsonContent.substring(firstBrace, lastBrace + 1)
          console.log('Extracted JSON object:', jsonContent.substring(0, 100) + '...')
        }
      }

      // 3. 清理可能的额外字符
      jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '')

      console.log('Final JSON content to parse:', jsonContent.substring(0, 200) + '...')

      // 使用改进的JSON解析逻辑
      const problemInfo = this.parseProblemJSONResponse(jsonContent)

      if (!problemInfo.description) {
        throw new Error('Problem description not found in the extracted data')
      }

      // 暂时禁用缓存写入以提高速度
      // await cacheService.set('problem_extraction', cacheKey, problemInfo)

      performanceMonitor.endOperation(operationId, true)
      return problemInfo
    }, 'Problem Extraction')
    } catch (error: any) {
      performanceMonitor.endOperation(operationId, false, error.message)
      throw error
    }
  }

  async generateSolution(problemInfo: ProblemInfo, language: string): Promise<SolutionResponse> {
    const operationId = performanceMonitor.startOperation('solution_generation', {
      language,
      model: this.modelName,
      problemTitle: problemInfo.title
    })

    try {
      if (!this.openai) {
        throw new Error('OpenAI client not initialized. Please check your API key.')
      }

      const languageName = LANGUAGE_MAPPING[language] || language

      // 检查缓存
      const cacheKey = { problemInfo, language, model: this.modelName }
      const cachedResult = await cacheService.get<SolutionResponse>('solution_generation', cacheKey)
      if (cachedResult) {
        console.log('[OpenAI] Using cached solution generation result')
        performanceMonitor.recordCacheHit('solution_generation')
        performanceMonitor.endOperation(operationId, true)
        return cachedResult
      }



    return RetryHelper.executeOpenAICall(async () => {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: `编程专家。快速JSON回复。thoughts用中文。`
        },
        {
          role: 'user',
          content: `${languageName}解决：

${problemInfo.title || ''}
${problemInfo.description}

JSON格式：
{"code":"代码","thoughts":["中文思路"],"time_complexity":"O()","space_complexity":"O()"}`
        }
      ]

      // 获取用户配置的token限制，如果没有配置则使用默认值
      const userConfig = aiConfigService.getConfig()
      const maxTokens = userConfig.maxTokens?.solutionGeneration || 2000 // 2K tokens for max speed

      const response = await RetryHelper.executeWithRetry(async () => {
        console.log('[OpenAI API] Sending solution generation request...')
        return await this.openai!.chat.completions.create({
          model: this.modelName,
          messages,
          max_tokens: maxTokens,
          temperature: userConfig.temperature || 0.0
        }, {
          timeout: userConfig.timeout?.solutionGeneration || 120000 // 增加超时到120秒
        })
      }, {
        maxAttempts: 1,  // 只尝试1次，最大速度
        baseDelay: 500,
        maxDelay: 2000,
        timeoutMs: userConfig.timeout?.solutionGeneration || 30000
      }, 'Solution Generation')

      // console.log('[OpenAI API] Solution response received') // 减少日志输出

      const content = response.choices[0]?.message?.content
      if (!content) {
        const errorDetails = {
          hasChoices: !!response.choices?.length,
          choicesCount: response.choices?.length || 0,
          hasMessage: !!response.choices?.[0]?.message,
          finishReason: response.choices?.[0]?.finish_reason,
          usage: response.usage
        }
        console.error('[OpenAI API] No content in solution response:', errorDetails)
        throw new Error(`No solution content from OpenAI API. Details: ${JSON.stringify(errorDetails)}`)
      }

      console.log('Raw API response length:', content.length)
      console.log('Raw API response preview:', content.substring(0, 200) + '...')

      // 改进的JSON解析逻辑
      const solution = this.parseJSONResponse(content)
      
      if (!solution.code || !solution.thoughts || !solution.time_complexity || !solution.space_complexity) {
        throw new Error('Incomplete solution generated')
      }

      // 缓存结果
      await cacheService.set('solution_generation', cacheKey, solution)

      performanceMonitor.endOperation(operationId, true)
      return solution
    }, 'Solution Generation')
    } catch (error: any) {
      performanceMonitor.endOperation(operationId, false, error.message)
      throw error
    }
  }

  async debugCode(imageDataList: string[], problemInfo: ProblemInfo, language: string): Promise<DebugResponse> {
    if (!this.openai) {
      throw new Error('OpenAI client not initialized. Please check your API key.')
    }

    // 这些参数保留用于未来功能扩展
    void problemInfo
    void language

    return RetryHelper.executeOpenAICall(async () => {
      const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [
        {
          role: 'system',
          content: '调试代码。JSON回复。中文thoughts。'
        },
        {
          role: 'user',
          content: [
            {
              type: 'text',
              text: `分析错误，修复代码。JSON：{"new_code":"","thoughts":["中文"],"time_complexity":"","space_complexity":""}`
            },
            ...imageDataList.map(imageData => ({
              type: 'image_url' as const,
              image_url: {
                url: `data:image/png;base64,${imageData}`,
                detail: 'high' as const
              }
            }))
          ]
        }
      ]

      // 获取用户配置
      const userConfig = aiConfigService.getConfig()
      const maxTokens = userConfig.maxTokens?.debugging || 1500 // 1.5K tokens for max speed

      const response = await RetryHelper.executeWithRetry(async () => {
        console.log('[OpenAI API] Sending debug request...')
        return await this.openai!.chat.completions.create({
          model: this.modelName,
          messages,
          max_tokens: maxTokens,
          temperature: userConfig.temperature || 0.0
        }, {
          timeout: userConfig.timeout?.debugging || 100000 // 100秒超时
        })
      }, {
        maxAttempts: 3,
        baseDelay: 2000,
        maxDelay: 10000,
        timeoutMs: userConfig.timeout?.debugging || 100000
      }, 'Code Debugging')

      console.log('[OpenAI API] Debug response received:', {
        choices: response.choices?.length || 0,
        hasContent: !!response.choices?.[0]?.message?.content,
        finishReason: response.choices?.[0]?.finish_reason,
        usage: response.usage
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        const errorDetails = {
          hasChoices: !!response.choices?.length,
          choicesCount: response.choices?.length || 0,
          hasMessage: !!response.choices?.[0]?.message,
          finishReason: response.choices?.[0]?.finish_reason,
          usage: response.usage
        }
        console.error('[OpenAI API] No content in debug response:', errorDetails)
        throw new Error(`No debug content from OpenAI API. Details: ${JSON.stringify(errorDetails)}`)
      }

      console.log('Raw API response:', content)

      // 更强健的 JSON 提取逻辑
      let jsonContent = content.trim()

      // 1. 首先尝试匹配 markdown 代码块
      const codeBlockMatch = jsonContent.match(/```(?:json)?\s*([\s\S]*?)\s*```/)
      if (codeBlockMatch) {
        jsonContent = codeBlockMatch[1].trim()
        console.log('Extracted from code block:', jsonContent.substring(0, 100) + '...')
      }

      // 2. 如果仍然不是以 { 开头，尝试找到第一个完整的 JSON 对象
      if (!jsonContent.startsWith('{')) {
        const firstBrace = jsonContent.indexOf('{')
        const lastBrace = jsonContent.lastIndexOf('}')
        if (firstBrace !== -1 && lastBrace !== -1 && firstBrace < lastBrace) {
          jsonContent = jsonContent.substring(firstBrace, lastBrace + 1)
          console.log('Extracted JSON object:', jsonContent.substring(0, 100) + '...')
        }
      }

      // 3. 清理可能的额外字符
      jsonContent = jsonContent.replace(/^[^{]*/, '').replace(/[^}]*$/, '')

      console.log('Final JSON content to parse:', jsonContent.substring(0, 200) + '...')

      const debugResult = JSON.parse(jsonContent) as DebugResponse
      
      if (!debugResult.new_code || !debugResult.thoughts || !debugResult.time_complexity || !debugResult.space_complexity) {
        throw new Error('Incomplete debug solution generated')
      }

      return debugResult
    }, 'Code Debugging')
  }
}

// 导出单例实例
export const openaiService = OpenAIService.getInstance()