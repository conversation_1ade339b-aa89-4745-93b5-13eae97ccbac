@tailwind base;
@tailwind components;
@tailwind utilities;

.frosted-glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(8px);
}

/* Custom select styles for transparent window */
select {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

select option {
  background-color: #1a1a1a !important;
  color: white !important;
}

/* Fix for select dropdown in transparent window */
select:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
}

/* Custom slider styles */
.slider {
  -webkit-appearance: none;
  appearance: none;
  background: rgba(255, 255, 255, 0.1);
  outline: none;
  border-radius: 8px;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  background: #f0f0f0;
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  background: #f0f0f0;
  transform: scale(1.1);
}

.auth-button {
  background: rgba(252, 252, 252, 0.98);
  color: rgba(60, 60, 60, 0.9);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 2;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.05);
}

.auth-button:hover {
  background: rgba(255, 255, 255, 1);
}

.auth-button::before {
  content: "";
  position: absolute;
  inset: -8px;
  background: linear-gradient(45deg, #ff000000, #0000ff00);
  z-index: -1;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: inherit;
  filter: blur(24px);
  opacity: 0;
}

.auth-button:hover::before {
  background: linear-gradient(
    45deg,
    rgba(255, 0, 0, 0.4),
    rgba(0, 0, 255, 0.4)
  );
  filter: blur(48px);
  inset: -16px;
  opacity: 1;
}

/* Hide scrollbars */
* {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

*::-webkit-scrollbar {
  display: none; /* WebKit */
}

/* Ensure body and html don't show scrollbars and have no default spacing */
html, body {
  overflow: hidden;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Reset all elements to have no default spacing */
*, *::before, *::after {
  box-sizing: border-box;
}

/* Ensure root container has no spacing */
#root {
  margin: 0;
  padding: 0;
  min-height: 0;
  min-width: 0;
}

/* Allow content to be scrollable but hide scrollbars */
.scrollable-content {
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.scrollable-content::-webkit-scrollbar {
  display: none;
}

/* Compact layout utilities */
.compact-container {
  @apply px-2 py-1.5;
}

.compact-spacing {
  @apply space-y-1.5;
}

.compact-button {
  @apply px-1.5 py-1 text-xs;
}

.compact-mini-button {
  @apply px-1 py-0.5 text-[10px];
}

.compact-gap {
  @apply gap-1.5;
}

.compact-gap-small {
  @apply gap-0.5;
}
