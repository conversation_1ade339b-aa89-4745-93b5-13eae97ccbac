import React from 'react'

interface CustomAlertProps {
  isOpen: boolean
  title?: string
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  onConfirm: () => void
  onCancel?: () => void
  confirmText?: string
  cancelText?: string
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  isOpen,
  title,
  message,
  type = 'info',
  onConfirm,
  onCancel,
  confirmText = '确定',
  cancelText = '取消'
}) => {
  if (!isOpen) return null

  const getIcon = () => {
    switch (type) {
      case 'success':
        return (
          <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        )
      case 'error':
        return (
          <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        )
      case 'warning':
        return (
          <svg className="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        )
      default:
        return (
          <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        )
    }
  }

  const getColorClasses = () => {
    switch (type) {
      case 'success':
        return 'border-green-400/50 shadow-green-400/20'
      case 'error':
        return 'border-red-400/50 shadow-red-400/20'
      case 'warning':
        return 'border-yellow-400/50 shadow-yellow-400/20'
      default:
        return 'border-blue-400/50 shadow-blue-400/20'
    }
  }

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center p-4">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black/70 backdrop-blur-md"
        onClick={onCancel}
      />

      {/* 弹窗内容 */}
      <div className={`relative bg-gray-900 border-2 ${getColorClasses()} rounded-xl shadow-2xl max-w-lg w-full overflow-hidden animate-fade-in animate-zoom-in`}>
        {/* 头部 */}
        {title && (
          <div className="px-6 py-4 border-b border-white/10">
            <div className="flex items-center gap-3">
              {getIcon()}
              <h3 className="text-lg font-semibold text-white">{title}</h3>
            </div>
          </div>
        )}
        
        {/* 内容 */}
        <div className="px-6 py-6">
          <div className="flex items-start gap-4">
            {!title && (
              <div className="flex-shrink-0 mt-1">
                {getIcon()}
              </div>
            )}
            <p className="text-gray-200 leading-relaxed text-base">{message}</p>
          </div>
        </div>
        
        {/* 按钮区域 */}
        <div className="px-6 py-5 bg-gray-800/80 border-t border-white/10">
          <div className="flex justify-end gap-3">
            {onCancel && (
              <button
                onClick={onCancel}
                className="px-6 py-2.5 text-sm font-medium text-gray-300 hover:text-white bg-gray-700 hover:bg-gray-600 rounded-lg transition-all duration-200 border border-gray-600 hover:border-gray-500"
              >
                {cancelText}
              </button>
            )}
            <button
              onClick={onConfirm}
              className="px-6 py-2.5 text-sm font-medium text-white bg-blue-600 hover:bg-blue-500 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 shadow-lg hover:shadow-blue-500/25"
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CustomAlert
