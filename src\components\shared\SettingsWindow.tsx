import React from "react"
import { COMMAND_KEY } from "../../utils/platform"

interface SettingsWindowProps {
  isOpen: boolean
  onClose: () => void
  currentLanguage: string
  setLanguage: (language: string) => void
}

const SettingsWindow: React.FC<SettingsWindowProps> = ({
  isOpen,
  onClose,
  currentLanguage,
  setLanguage
}) => {
  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9998]"
        onClick={onClose}
      />
      
      {/* Settings Window */}
      <div className="fixed inset-0 flex items-center justify-center z-[9999] p-4">
        <div className="bg-gray-900 border border-gray-700 rounded-lg shadow-2xl min-w-80 max-w-md w-auto">
          {/* Header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-700">
            <h2 className="text-lg font-semibold text-white">Settings</h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-1"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* Content */}
          <div className="p-4 space-y-4">
            {/* Keyboard Shortcuts Section */}
            <div>
              <h3 className="text-sm font-medium text-white mb-2">Keyboard Shortcuts</h3>
              <div className="space-y-1.5">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Toggle Window</span>
                  <div className="flex gap-1">
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      {COMMAND_KEY}
                    </kbd>
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      B
                    </kbd>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Take Screenshot</span>
                  <div className="flex gap-1">
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      {COMMAND_KEY}
                    </kbd>
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      H
                    </kbd>
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Solve</span>
                  <div className="flex gap-1">
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      {COMMAND_KEY}
                    </kbd>
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      Enter
                    </kbd>
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-300">Start Over</span>
                  <div className="flex gap-1">
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      {COMMAND_KEY}
                    </kbd>
                    <kbd className="bg-gray-700 rounded px-2 py-1 text-xs text-gray-300">
                      R
                    </kbd>
                  </div>
                </div>
              </div>
            </div>

            {/* Language Selection */}
            <div>
              <h3 className="text-sm font-medium text-white mb-2">Programming Language</h3>
              <select 
                className="w-full bg-gray-800 border border-gray-600 rounded-md px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                value={currentLanguage}
                onChange={(e) => setLanguage(e.target.value)}
              >
                <option value="python">Python</option>
                <option value="javascript">JavaScript</option>
                <option value="typescript">TypeScript</option>
                <option value="java">Java</option>
                <option value="cpp">C++</option>
                <option value="c">C</option>
                <option value="csharp">C#</option>
                <option value="go">Go</option>
                <option value="rust">Rust</option>
                <option value="php">PHP</option>
                <option value="ruby">Ruby</option>
                <option value="swift">Swift</option>
                <option value="kotlin">Kotlin</option>
                <option value="scala">Scala</option>
              </select>
            </div>

            {/* Quick Info */}
            <div className="text-center">
              <p className="text-xs text-gray-400">
                Press ESC or click outside to close
              </p>
            </div>
          </div>


        </div>
      </div>
    </>
  )
}

export default SettingsWindow
