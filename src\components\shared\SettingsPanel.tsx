import React, { useState, useEffect, useRef } from "react"
import { COMMAND_KEY } from "../../utils/platform"
import { useCustomAlert } from "../../hooks/useCustomAlert"

interface AIConfig {
  apiUrl: string
  apiKey: string
  model: string
}

const languages = [
  { value: "python", label: "Python" },
  { value: "javascript", label: "JavaScript" },
  { value: "typescript", label: "TypeScript" },
  { value: "java", label: "Java" },
  { value: "cpp", label: "C++" },
  { value: "c", label: "C" },
  { value: "csharp", label: "C#" },
  { value: "go", label: "Go" },
  { value: "rust", label: "Rust" },
  { value: "php", label: "PHP" },
  { value: "ruby", label: "Ruby" },
  { value: "swift", label: "Swift" },
  { value: "kotlin", label: "<PERSON><PERSON><PERSON>" },
  { value: "scala", label: "<PERSON>ala" },
  { value: "dart", label: "Dar<PERSON>" },
  { value: "sql", label: "SQL" },
  { value: "r", label: "R" }
]

interface CustomLanguageSelectorProps {
  currentLanguage: string
  setLanguage: (language: string) => void
}

const CustomLanguageSelector: React.FC<CustomLanguageSelectorProps> = ({
  currentLanguage,
  setLanguage
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const currentLang = languages.find(lang => lang.value === currentLanguage)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full bg-white/10 border border-white/20 rounded-md px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent flex items-center justify-between"
      >
        <span>{currentLang?.label || "Select Language"}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            fill="#ffffff"
            d="M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className="fixed inset-0 z-[10000]"
          style={{ backgroundColor: 'rgba(0,0,0,0.3)' }}
          onClick={() => setIsOpen(false)}
        >
          <div
            className="absolute backdrop-blur-md bg-black/90 rounded-lg border border-white/20 overflow-hidden"
            style={{
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: dropdownRef.current?.offsetWidth + 'px' || '200px',
              maxHeight: '300px',
              overflowY: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="py-1">
              {languages.map((lang) => (
                <button
                  key={lang.value}
                  onClick={() => {
                    setLanguage(lang.value)
                    setIsOpen(false)
                  }}
                  className={`w-full px-3 py-2 text-sm text-left hover:bg-white/10 transition-colors text-white/90 ${
                    currentLanguage === lang.value ? 'bg-white/10' : ''
                  }`}
                >
                  {lang.label}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface CustomModelSelectorProps {
  availableModels: string[]
  currentModel: string
  onModelChange: (model: string) => void
}

const CustomModelSelector: React.FC<CustomModelSelectorProps> = ({
  availableModels,
  currentModel,
  onModelChange
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div ref={dropdownRef} className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full bg-white/10 border border-white/20 rounded-md px-3 py-2 text-sm text-white focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent flex items-center justify-between"
      >
        <span>{currentModel}</span>
        <svg
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 12 12"
        >
          <path
            fill="#ffffff"
            d="M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className="fixed inset-0 z-[10000]"
          style={{ backgroundColor: 'rgba(0,0,0,0.3)' }}
          onClick={() => setIsOpen(false)}
        >
          <div
            className="absolute backdrop-blur-md bg-black/90 rounded-lg border border-white/20 overflow-hidden"
            style={{
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: dropdownRef.current?.offsetWidth + 'px' || '200px',
              maxHeight: '300px',
              overflowY: 'auto'
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="py-1">
              {availableModels.map((model) => (
                <button
                  key={model}
                  onClick={() => {
                    onModelChange(model)
                    setIsOpen(false)
                  }}
                  className={`w-full px-3 py-2 text-sm text-left hover:bg-white/10 transition-colors text-white/90 ${
                    currentModel === model ? 'bg-white/10' : ''
                  }`}
                >
                  {model}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

interface SettingsPanelProps {
  isOpen: boolean
  currentLanguage: string
  setLanguage: (language: string) => void
  onTakeScreenshot?: () => void
  onSolve?: () => void
  onStartOver?: () => void
  onToggleWindow?: () => void
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  isOpen,
  currentLanguage,
  setLanguage,
  onTakeScreenshot,
  onSolve,
  onStartOver,
  onToggleWindow
}) => {
  const [activeTab, setActiveTab] = useState<'actions' | 'ai'>('actions')
  const [aiConfig, setAiConfig] = useState<AIConfig>({
    apiUrl: 'https://api.openai.com/v1',
    apiKey: '',
    model: 'gpt-4o'
  })
  const [showApiKey, setShowApiKey] = useState(false)

  const [availableModels, setAvailableModels] = useState<string[]>([])
  const [loadingModels, setLoadingModels] = useState(false)

  const { showSuccess, showError, AlertComponent } = useCustomAlert()

  // 加载AI配置
  useEffect(() => {
    if (isOpen) {
      loadAIConfig()
    }
  }, [isOpen])

  // 当打开AI设置时，检查是否需要获取模型列表
  useEffect(() => {
    const handleModelsOnSettingsOpen = async () => {
      // 只在打开AI设置标签且配置完整时处理
      if (!isOpen || activeTab !== 'ai' || !aiConfig.apiKey || !aiConfig.apiUrl) {
        return
      }

      // 如果已经有模型列表，直接使用
      if (availableModels.length > 0) {
        return
      }

      // 检查是否在启动时已加载过模型
      if (window.__MODELS_LOADED_ON_STARTUP__) {
        try {
          // 从后端获取已缓存的模型列表
          const result = await window.electronAPI.getAvailableModels()
          if (result.success && result.models && result.models.length > 0) {
            setAvailableModels(result.models)
            console.log(`Settings: Loaded ${result.models.length} models from cache`)
          }
        } catch (error) {
          console.error('Settings: Error getting models from cache:', error)
        }
      }
    }

    handleModelsOnSettingsOpen()
  }, [isOpen, activeTab, aiConfig.apiKey, aiConfig.apiUrl, availableModels.length])

  const loadAIConfig = async () => {
    try {
      const config = await window.electronAPI.getAIConfig()
      if (config) {
        setAiConfig({
          apiUrl: config.apiUrl || 'https://api.openai.com/v1',
          apiKey: config.apiKey || '',
          model: config.model || 'gpt-4o'
        })
      }
    } catch (error) {
      console.error('Failed to load AI config:', error)
    }
  }



  const updateAIConfig = async (updates: Partial<AIConfig>) => {
    try {
      // 输入验证和清理
      const sanitizedUpdates = sanitizeConfigInput(updates)
      const newConfig = { ...aiConfig, ...sanitizedUpdates }
      setAiConfig(newConfig)

      await window.electronAPI.updateAIConfig(newConfig)

      // 如果更新了 API URL 或 API Key，清空模型列表
      if (updates.apiUrl || updates.apiKey) {
        setAvailableModels([])
        console.log('API configuration changed, cleared model list')
      }
    } catch (error) {
      console.error('Failed to update AI config:', error)
    }
  }

  const sanitizeConfigInput = (input: Partial<AIConfig>): Partial<AIConfig> => {
    const sanitized: Partial<AIConfig> = {}

    // 清理API URL
    if (input.apiUrl !== undefined) {
      sanitized.apiUrl = input.apiUrl.trim()
      // 验证URL格式
      if (sanitized.apiUrl && !isValidUrl(sanitized.apiUrl)) {
        console.warn('Invalid API URL format')
        return {} // 不更新无效的URL
      }
    }

    // 清理API密钥
    if (input.apiKey !== undefined) {
      sanitized.apiKey = input.apiKey.trim()
    }

    // 清理模型名称
    if (input.model !== undefined) {
      sanitized.model = input.model.trim()
      // 验证模型名称格式（只允许字母、数字、连字符、下划线、点）
      if (sanitized.model && !/^[a-zA-Z0-9\-_.]+$/.test(sanitized.model)) {
        console.warn('Invalid model name format')
        return {} // 不更新无效的模型名称
      }
    }

    return sanitized
  }

  const isValidUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url)
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:'
    } catch {
      return false
    }
  }

  const testConnection = async () => {
    try {
      console.log('Testing AI connection...')
      const result = await window.electronAPI.testAIConnection()
      if (result.success) {
        console.log('Connection test successful')
        await showSuccess('连接成功！', 'API 连接测试')
        // 连接成功后不自动刷新模型列表，用户需要手动点击刷新
      } else {
        console.error('Connection test failed:', result.error)
        await showError(`连接失败：${result.error}`, 'API 连接测试')
      }
    } catch (error) {
      console.error('Connection test error:', error)
      await showError(`连接测试失败：${error}`, 'API 连接测试')
    }
  }

  const fetchAvailableModels = async (forceRefresh: boolean = false) => {
    if (!aiConfig.apiKey || !aiConfig.apiUrl) {
      console.log('Settings: API configuration incomplete')
      return
    }

    // 如果不是强制刷新且已有模型列表，直接返回
    if (!forceRefresh && availableModels.length > 0) {
      return
    }

    setLoadingModels(true)

    if (forceRefresh) {
      // 强制刷新时先清除后端缓存
      try {
        await window.electronAPI.clearModelsCache()
      } catch (error) {
        console.warn('Settings: Failed to clear backend cache:', error)
      }
    }

    try {
      const result = await window.electronAPI.getAvailableModels()
      if (result.success && result.models) {
        setAvailableModels(result.models)
        console.log(`Settings: Got ${result.models.length} models`)

        // 如果当前选择的模型不在列表中，自动选择第一个模型
        if (result.models.length > 0 && !result.models.includes(aiConfig.model)) {
          updateAIConfig({ model: result.models[0] })
        }
      } else {
        // 设置默认模型列表
        const defaultModels = [
          'gpt-4o',
          'gpt-4o-mini',
          'gpt-4-turbo',
          'gpt-4',
          'gpt-3.5-turbo',
          'claude-3-5-sonnet',
          'claude-3-sonnet',
          'claude-3-haiku',
          'claude-3-opus'
        ]
        setAvailableModels(defaultModels)
        console.log('Settings: Using default model list')
      }
    } catch (error) {
      console.error('Settings: Error fetching models:', error)
      // 设置默认模型列表
      const defaultModels = [
        'gpt-4o',
        'gpt-4o-mini',
        'gpt-4-turbo',
        'gpt-4',
        'gpt-3.5-turbo',
        'claude-3-5-sonnet',
        'claude-3-sonnet',
        'claude-3-haiku',
        'claude-3-opus'
      ]
      setAvailableModels(defaultModels)
      console.log('Settings: Using default model list due to error')
    } finally {
      setLoadingModels(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="mt-2 w-fit">
      <div className="backdrop-blur-md bg-black/60 rounded-lg p-4 border border-white/10 min-w-[500px]">
        {/* Tab Navigation */}
        <div className="flex gap-1 mb-4 bg-white/5 rounded-lg p-1">
          <button
            onClick={() => setActiveTab('actions')}
            className={`flex-1 px-3 py-2 text-xs rounded-md transition-colors ${
              activeTab === 'actions'
                ? 'bg-white/20 text-white'
                : 'text-gray-300 hover:text-white hover:bg-white/10'
            }`}
          >
            Quick Actions
          </button>
          <button
            onClick={() => setActiveTab('ai')}
            className={`flex-1 px-3 py-2 text-xs rounded-md transition-colors ${
              activeTab === 'ai'
                ? 'bg-white/20 text-white'
                : 'text-gray-300 hover:text-white hover:bg-white/10'
            }`}
          >
            AI Settings
          </button>

        </div>

        <div className="space-y-4">
          {/* Quick Actions Tab */}
          {activeTab === 'actions' && (
            <>
              {/* Quick Actions Section */}
              <div>
            <h3 className="text-sm font-medium text-white mb-3">Quick Actions</h3>
            <div className="space-y-1">
              {/* Toggle Window */}
              {onToggleWindow && (
                <div
                  className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                  onClick={onToggleWindow}
                >
                  <span className="text-[11px] leading-none text-gray-300">
                    Toggle Window
                  </span>
                  <div className="flex gap-1">
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      {COMMAND_KEY}
                    </button>
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      B
                    </button>
                  </div>
                </div>
              )}

              {/* Take Screenshot */}
              {onTakeScreenshot && (
                <div
                  className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                  onClick={onTakeScreenshot}
                >
                  <span className="text-[11px] leading-none text-gray-300">
                    Take Screenshot
                  </span>
                  <div className="flex gap-1">
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      {COMMAND_KEY}
                    </button>
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      H
                    </button>
                  </div>
                </div>
              )}

              {/* Solve */}
              {onSolve && (
                <div
                  className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                  onClick={onSolve}
                >
                  <span className="text-[11px] leading-none text-gray-300">
                    Solve
                  </span>
                  <div className="flex gap-1">
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      {COMMAND_KEY}
                    </button>
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      ↵
                    </button>
                  </div>
                </div>
              )}

              {/* Start Over */}
              {onStartOver && (
                <div
                  className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-white/10 transition-colors"
                  onClick={onStartOver}
                >
                  <span className="text-[11px] leading-none text-gray-300">
                    Start Over
                  </span>
                  <div className="flex gap-1">
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      {COMMAND_KEY}
                    </button>
                    <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                      R
                    </button>
                  </div>
                </div>
              )}

              {/* Quit Application */}
              <div
                className="flex items-center gap-2 cursor-pointer rounded px-2 py-1.5 hover:bg-red-500/20 transition-colors text-red-400"
                onClick={async () => {
                  try {
                    await window.electronAPI.quitApp()
                  } catch (error) {
                    console.error('Failed to quit app:', error)
                  }
                }}
              >
                <span className="text-[11px] leading-none">
                  Quit Application
                </span>
                <div className="flex gap-1">
                  <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                    {COMMAND_KEY}
                  </button>
                  <button className="bg-white/10 rounded-md px-1.5 py-1 text-[11px] leading-none text-white/70">
                    Q
                  </button>
                </div>
              </div>
            </div>
          </div>

              {/* Language Selection */}
              <div>
                <h3 className="text-sm font-medium text-white mb-3">Programming Language</h3>
                <CustomLanguageSelector
                  currentLanguage={currentLanguage}
                  setLanguage={setLanguage}
                />
              </div>
            </>
          )}

          {/* AI Settings Tab */}
          {activeTab === 'ai' && (
            <>
              {/* API URL */}
              <div>
                <h3 className="text-sm font-medium text-white mb-3">API URL</h3>
                <input
                  type="text"
                  placeholder="https://api.openai.com/v1"
                  value={aiConfig.apiUrl}
                  onChange={(e) => updateAIConfig({ apiUrl: e.target.value })}
                  className="w-full bg-white/10 border border-white/20 rounded-md px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                />
                <p className="text-xs text-gray-400 mt-1">
                  支持 OpenAI API 或兼容的 API 服务
                </p>
              </div>

              {/* API Key */}
              <div>
                <h3 className="text-sm font-medium text-white mb-3">API Key</h3>
                <div className="relative">
                  <input
                    type={showApiKey ? "text" : "password"}
                    placeholder="sk-..."
                    value={aiConfig.apiKey}
                    onChange={(e) => updateAIConfig({ apiKey: e.target.value })}
                    className="w-full bg-white/10 border border-white/20 rounded-md px-3 py-2 pr-10 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                  />
                  <button
                    type="button"
                    onClick={() => setShowApiKey(!showApiKey)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
                  >
                    {showApiKey ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    )}
                  </button>
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  您的 API 密钥将安全存储在本地
                </p>
              </div>

              {/* Model Selection */}
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-white">模型</h3>
                  <div className="flex items-center gap-2">
                    {loadingModels && (
                      <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
                    )}
                    <button
                      onClick={() => fetchAvailableModels(true)}
                      disabled={!aiConfig.apiKey || !aiConfig.apiUrl || loadingModels}
                      className="text-xs text-blue-400 hover:text-blue-300 disabled:text-gray-500 disabled:cursor-not-allowed flex items-center gap-1"
                      title="刷新模型列表"
                    >
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      {loadingModels ? '加载中...' : '刷新'}
                    </button>
                  </div>
                </div>

                {availableModels.length > 0 ? (
                  <div className="space-y-2">
                    <CustomModelSelector
                      availableModels={availableModels}
                      currentModel={aiConfig.model}
                      onModelChange={(model) => updateAIConfig({ model })}
                    />
                    <p className="text-xs text-green-400 flex items-center gap-1">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      找到 {availableModels.length} 个可用模型
                    </p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <input
                      type="text"
                      placeholder="gpt-4o"
                      value={aiConfig.model}
                      onChange={(e) => updateAIConfig({ model: e.target.value })}
                      className="w-full bg-white/10 border border-white/20 rounded-md px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-white/30 focus:border-transparent"
                    />
                    <p className="text-xs text-yellow-400 flex items-center gap-1">
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      手动输入模型名称。配置API密钥和URL后，重启程序或点击刷新按钮获取可用模型列表
                    </p>
                  </div>
                )}
              </div>

              {/* Test Connection Button */}
              <div className="pt-2 border-t border-white/10">
                <button
                  onClick={testConnection}
                  disabled={!aiConfig.apiKey || !aiConfig.apiUrl}
                  className="w-full bg-blue-600/20 hover:bg-blue-600/30 disabled:bg-gray-600/20 disabled:text-gray-500 text-blue-400 text-sm px-3 py-2 rounded transition-colors"
                >
                  测试连接
                </button>
              </div>

              {/* Reset Button */}
              <div>
                <button
                  onClick={async () => {
                    try {
                      await window.electronAPI.resetAIConfig()
                      await loadAIConfig()
                    } catch (error) {
                      console.error('Failed to reset AI config:', error)
                    }
                  }}
                  className="w-full bg-red-600/20 hover:bg-red-600/30 text-red-400 text-sm px-3 py-2 rounded transition-colors"
                >
                  重置设置
                </button>
              </div>
            </>
          )}



          {/* Quick Info */}
          <div className="text-center pt-2 border-t border-white/10">
            <p className="text-xs text-gray-400">
              Click Settings again to close
            </p>
          </div>
        </div>
      </div>
      {/* 自定义弹窗 */}
      <AlertComponent />
    </div>
  )
}

export default SettingsPanel
