// CacheService.ts - 智能缓存服务
import crypto from 'crypto'
import fs from 'fs'
import path from 'path'
import { app } from 'electron'

export interface CacheEntry<T> {
  data: T
  timestamp: number
  hash: string
  expiresAt: number
}

export class CacheService {
  private cacheDir: string
  private memoryCache: Map<string, CacheEntry<any>> = new Map()
  private readonly DEFAULT_TTL = 2 * 60 * 60 * 1000
  private readonly MAX_MEMORY_ENTRIES = 50
  private cleanupTimer?: NodeJS.Timeout

  constructor() {
    this.cacheDir = path.join(app.getPath('userData'), 'ai_cache')
    this.ensureCacheDir()
    this.loadMemoryCache()

    // 启动定期清理定时器
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, 30 * 60 * 1000)
  }

  private ensureCacheDir(): void {
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true })
    }
  }

  private loadMemoryCache(): void {
    try {
      const files = fs.readdirSync(this.cacheDir)
      for (const file of files.slice(0, this.MAX_MEMORY_ENTRIES)) {
        if (file.endsWith('.json')) {
          const filePath = path.join(this.cacheDir, file)
          const content = fs.readFileSync(filePath, 'utf8')
          const entry = JSON.parse(content) as CacheEntry<any>
          
          // 检查是否过期
          if (entry.expiresAt > Date.now()) {
            const key = file.replace('.json', '')
            this.memoryCache.set(key, entry)
          } else {
            // 删除过期文件
            fs.unlinkSync(filePath)
          }
        }
      }
      if (this.memoryCache.size > 0) {
        console.log(`[Cache] Loaded ${this.memoryCache.size} entries into memory`)
      }
    } catch (error) {
      console.error('[Cache] Error loading memory cache:', error)
    }
  }

  /**
   * 生成缓存键
   */
  private generateCacheKey(type: string, data: any): string {
    const hash = crypto.createHash('sha256')
    hash.update(JSON.stringify({ type, data }))
    return `${type}_${hash.digest('hex')}`
  }

  /**
   * 获取缓存
   */
  async get<T>(type: string, data: any): Promise<T | null> {
    const key = this.generateCacheKey(type, data)
    
    // 先检查内存缓存
    const memoryEntry = this.memoryCache.get(key)
    if (memoryEntry && memoryEntry.expiresAt > Date.now()) {
      // console.log(`[Cache] Memory hit for ${type}`) // 减少日志输出
      return memoryEntry.data as T
    }

    // 检查磁盘缓存
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`)
      if (fs.existsSync(filePath)) {
        const content = fs.readFileSync(filePath, 'utf8')
        const entry = JSON.parse(content) as CacheEntry<T>
        
        if (entry.expiresAt > Date.now()) {
          // 加载到内存缓存
          this.memoryCache.set(key, entry)
          // console.log(`[Cache] Disk hit for ${type}`) // 减少日志输出
          return entry.data
        } else {
          // 删除过期文件
          fs.unlinkSync(filePath)
        }
      }
    } catch (error) {
      console.error(`[Cache] Error reading cache for ${type}:`, error)
    }

    return null
  }

  /**
   * 设置缓存
   */
  async set<T>(type: string, data: any, result: T, ttl: number = this.DEFAULT_TTL): Promise<void> {
    const key = this.generateCacheKey(type, data)
    const entry: CacheEntry<T> = {
      data: result,
      timestamp: Date.now(),
      hash: key,
      expiresAt: Date.now() + ttl
    }

    // 保存到内存缓存
    this.memoryCache.set(key, entry)

    // 保存到磁盘缓存
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`)
      fs.writeFileSync(filePath, JSON.stringify(entry, null, 2), { mode: 0o600 })
      // console.log(`[Cache] Saved ${type} to cache`) // 减少日志输出
    } catch (error) {
      console.error(`[Cache] Error saving cache for ${type}:`, error)
    }

    // 清理内存缓存
    this.cleanupMemoryCache()
  }

  /**
   * 清理内存缓存
   */
  private cleanupMemoryCache(): void {
    // 首先清理过期条目
    const now = Date.now()
    for (const [key, entry] of this.memoryCache.entries()) {
      if (entry.expiresAt <= now) {
        this.memoryCache.delete(key)
      }
    }

    // 如果仍然超过限制，删除最旧的条目
    if (this.memoryCache.size > this.MAX_MEMORY_ENTRIES) {
      const entries = Array.from(this.memoryCache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

      // 删除最旧的条目
      const toDelete = entries.slice(0, entries.length - this.MAX_MEMORY_ENTRIES)
      for (const [key] of toDelete) {
        this.memoryCache.delete(key)
      }
    }
  }

  /**
   * 清理过期缓存
   */
  async cleanup(): Promise<void> {
    try {
      const files = fs.readdirSync(this.cacheDir)
      let deletedCount = 0

      for (const file of files) {
        if (file.endsWith('.json')) {
          const filePath = path.join(this.cacheDir, file)
          try {
            const content = fs.readFileSync(filePath, 'utf8')
            const entry = JSON.parse(content) as CacheEntry<any>
            
            if (entry.expiresAt <= Date.now()) {
              fs.unlinkSync(filePath)
              deletedCount++
            }
          } catch (error) {
            // 删除损坏的缓存文件
            fs.unlinkSync(filePath)
            deletedCount++
          }
        }
      }

      // 清理内存缓存中的过期条目
      for (const [key, entry] of this.memoryCache.entries()) {
        if (entry.expiresAt <= Date.now()) {
          this.memoryCache.delete(key)
        }
      }

      console.log(`[Cache] Cleaned up ${deletedCount} expired entries`)
    } catch (error) {
      console.error('[Cache] Error during cleanup:', error)
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    try {
      const files = fs.readdirSync(this.cacheDir)
      for (const file of files) {
        if (file.endsWith('.json')) {
          fs.unlinkSync(path.join(this.cacheDir, file))
        }
      }
      this.memoryCache.clear()
      console.log('[Cache] Cleared all cache')
    } catch (error) {
      console.error('[Cache] Error clearing cache:', error)
    }
  }

  /**
   * 销毁缓存服务
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    this.memoryCache.clear()
    console.log('[Cache] Cache service destroyed')
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): { memoryEntries: number; diskEntries: number; totalSize: string } {
    let diskEntries = 0
    let totalSize = 0

    try {
      const files = fs.readdirSync(this.cacheDir)
      for (const file of files) {
        if (file.endsWith('.json')) {
          diskEntries++
          const filePath = path.join(this.cacheDir, file)
          totalSize += fs.statSync(filePath).size
        }
      }
    } catch (error) {
      console.error('[Cache] Error getting stats:', error)
    }

    return {
      memoryEntries: this.memoryCache.size,
      diskEntries,
      totalSize: `${(totalSize / 1024 / 1024).toFixed(2)} MB`
    }
  }
}

// 单例实例
export const cacheService = new CacheService()
