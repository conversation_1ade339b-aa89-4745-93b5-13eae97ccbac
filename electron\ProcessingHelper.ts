// ProcessingHelper.ts
import fs from "node:fs"
import { <PERSON>shotHelper } from "./ScreenshotHelper"
import { IProcessingHelperDeps } from "./main"
import { BrowserWindow } from "electron"
import { openaiService, ProblemInfo, SolutionResponse, DebugResponse } from "./services/openaiService"
import { ErrorHandler } from "./utils/ErrorHandler"
import { RetryHelper } from "./utils/RetryHelper"

export class ProcessingHelper {
  private deps: IProcessingHelperDeps
  private screenshotHelper: ScreenshotHelper

  // AbortControllers for API requests
  private currentProcessingAbortController: AbortController | null = null
  private currentExtraProcessingAbortController: AbortController | null = null

  constructor(deps: IProcessingHelperDeps) {
    this.deps = deps
    this.screenshotHelper = deps.getScreenshotHelper()
  }

  private async waitForInitialization(
    mainWindow: BrowserWindow
  ): Promise<void> {
    let attempts = 0
    const maxAttempts = 50 // 5 seconds total

    while (attempts < maxAttempts) {
      const isInitialized = await mainWindow.webContents.executeJavaScript(
        "window.__IS_INITIALIZED__"
      )
      if (isInitialized) return
      await new Promise((resolve) => setTimeout(resolve, 100))
      attempts++
    }
    throw new Error("App failed to initialize after 5 seconds")
  }

  // Credits method removed - unlimited usage

  private async getLanguage(): Promise<string> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return "python"

    try {
      await this.waitForInitialization(mainWindow)
      const language = await mainWindow.webContents.executeJavaScript(
        "window.__LANGUAGE__"
      )

      if (
        typeof language !== "string" ||
        language === undefined ||
        language === null
      ) {
        console.warn("Language not properly initialized")
        return "python"
      }

      return language
    } catch (error) {
      console.error("Error getting language:", error)
      return "python"
    }
  }

  // Auth token removed - no authentication needed
  private async getAuthToken(): Promise<string | null> {
    return "no-auth-needed"
  }

  public async processScreenshots(): Promise<void> {
    const mainWindow = this.deps.getMainWindow()
    if (!mainWindow) return

    // Credits check removed - unlimited usage

    const view = this.deps.getView()
    console.log("Processing screenshots in view:", view)

    if (view === "queue") {
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.INITIAL_START)
      const screenshotQueue = this.screenshotHelper.getScreenshotQueue()
      console.log("Processing main queue screenshots:", screenshotQueue)
      if (screenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }

      try {
        // Initialize AbortController
        this.currentProcessingAbortController = new AbortController()
        const { signal } = this.currentProcessingAbortController

        const screenshots = await Promise.all(
          screenshotQueue.map(async (path) => ({
            path,
            preview: await this.screenshotHelper.getImagePreview(path),
            data: fs.readFileSync(path).toString("base64")
          }))
        )

        const result = await this.processScreenshotsHelper(screenshots, signal)

        if (!result.success) {
          console.log("Processing failed:", result.error)
          const userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(result.error)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
            userFriendlyMessage
          )
          // Reset view back to queue on error
          console.log("Resetting view to queue due to error")
          this.deps.setView("queue")
          return
        }

        // Only set view to solutions if processing succeeded
        console.log("Setting view to solutions after successful processing")
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
          result.data
        )
        this.deps.setView("solutions")
      } catch (error: any) {
        console.error("Processing error:", error)
        const userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(error)
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.INITIAL_SOLUTION_ERROR,
          userFriendlyMessage
        )
        // Reset view back to queue on error
        console.log("Resetting view to queue due to error")
        this.deps.setView("queue")
      } finally {
        this.currentProcessingAbortController = null
      }
    } else {
      // view == 'solutions'
      const extraScreenshotQueue =
        this.screenshotHelper.getExtraScreenshotQueue()
      console.log("Processing extra queue screenshots:", extraScreenshotQueue)
      if (extraScreenshotQueue.length === 0) {
        mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
        return
      }
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.DEBUG_START)

      // Initialize AbortController
      this.currentExtraProcessingAbortController = new AbortController()
      const { signal } = this.currentExtraProcessingAbortController

      try {
        const screenshots = await Promise.all(
          [
            ...this.screenshotHelper.getScreenshotQueue(),
            ...extraScreenshotQueue
          ].map(async (path) => ({
            path,
            preview: await this.screenshotHelper.getImagePreview(path),
            data: fs.readFileSync(path).toString("base64")
          }))
        )
        console.log(
          "Combined screenshots for processing:",
          screenshots.map((s) => s.path)
        )

        const result = await this.processExtraScreenshotsHelper(
          screenshots,
          signal
        )

        if (result.success) {
          this.deps.setHasDebugged(true)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_SUCCESS,
            result.data
          )
        } else {
          const userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(result.error)
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
            userFriendlyMessage
          )
        }
      } catch (error: any) {
        const userFriendlyMessage = ErrorHandler.getUserFriendlyMessage(error)
        mainWindow.webContents.send(
          this.deps.PROCESSING_EVENTS.DEBUG_ERROR,
          userFriendlyMessage
        )
      } finally {
        this.currentExtraProcessingAbortController = null
      }
    }
  }

  private async processScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const imageDataList = screenshots.map((screenshot) => screenshot.data)
      const mainWindow = this.deps.getMainWindow()
      const language = await this.getLanguage()
      
      // Check if request was cancelled
      if (signal.aborted) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      // First API call - extract problem info
      try {
        const problemInfo = await openaiService.extractProblem(imageDataList, language)

        // Store problem info in AppState
        this.deps.setProblemInfo(problemInfo)

        // Send first success event
        if (mainWindow) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.PROBLEM_EXTRACTED,
            problemInfo
          )

          // Generate solutions after successful extraction
          const solutionsResult = await this.generateSolutionsHelper(signal)
          if (solutionsResult.success) {
            // Clear any existing extra screenshots before transitioning to solutions view
            this.screenshotHelper.clearExtraScreenshotQueue()
            mainWindow.webContents.send(
              this.deps.PROCESSING_EVENTS.SOLUTION_SUCCESS,
              solutionsResult.data
            )
            return { success: true, data: solutionsResult.data }
          } else {
            throw new Error(
              solutionsResult.error || "Failed to generate solutions"
            )
          }
        }
      } catch (error: any) {
        // If the request was cancelled, don't retry
        if (signal.aborted) {
          return {
            success: false,
            error: "Processing was canceled by the user."
          }
        }

        console.error("OpenAI API Error:", error)

        // Handle specific OpenAI errors
        if (error.message?.includes('API key')) {
          throw new Error(
            "OpenAI API key not found or invalid. Please set the OPENAI_API_KEY environment variable."
          )
        }

        // If we get here, it's an unknown error
        throw new Error(error.message || "Failed to process screenshots. Please try again.")
      }
    } catch (error: any) {
      console.error("Processing error:", error)
      return { success: false, error: error.message }
    }
  }

  private async generateSolutionsHelper(signal: AbortSignal) {
    try {
      const problemInfo = this.deps.getProblemInfo()
      const language = await this.getLanguage()

      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      // Check if request was cancelled
      if (signal.aborted) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      const solution = await openaiService.generateSolution(problemInfo, language)
      
      // Format the response to match the expected structure
      const formattedResponse = {
        success: true,
        data: {
          code: solution.code,
          thoughts: solution.thoughts,
          time_complexity: solution.time_complexity,
          space_complexity: solution.space_complexity
        }
      }

      return { success: true, data: formattedResponse.data }
    } catch (error: any) {
      const mainWindow = this.deps.getMainWindow()

      // Handle specific errors
      if (error.message?.includes('API key')) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.message }
      }

      return { success: false, error: error.message || "Failed to generate solution" }
    }
  }

  private async processExtraScreenshotsHelper(
    screenshots: Array<{ path: string; data: string }>,
    signal: AbortSignal
  ) {
    try {
      const imageDataList = screenshots.map((screenshot) => screenshot.data)
      const problemInfo = this.deps.getProblemInfo() as ProblemInfo
      const language = await this.getLanguage()

      if (!problemInfo) {
        throw new Error("No problem info available")
      }

      // Check if request was cancelled
      if (signal.aborted) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      const debugResult = await openaiService.debugCode(imageDataList, problemInfo, language)
      
      // Format the response to match the expected structure
      const formattedResponse = {
        success: true,
        data: {
          new_code: debugResult.new_code,
          thoughts: debugResult.thoughts,
          time_complexity: debugResult.time_complexity,
          space_complexity: debugResult.space_complexity
        }
      }

      return { success: true, data: formattedResponse.data }
    } catch (error: any) {
      const mainWindow = this.deps.getMainWindow()

      // Handle cancellation
      if (signal.aborted) {
        return {
          success: false,
          error: "Processing was canceled by the user."
        }
      }

      // Handle specific errors
      if (error.message?.includes('API key')) {
        if (mainWindow) {
          mainWindow.webContents.send(
            this.deps.PROCESSING_EVENTS.API_KEY_INVALID
          )
        }
        return { success: false, error: error.message }
      }

      return { success: false, error: error.message || "Failed to debug code" }
    }
  }

  public cancelOngoingRequests(): void {
    let wasCancelled = false

    if (this.currentProcessingAbortController) {
      this.currentProcessingAbortController.abort()
      this.currentProcessingAbortController = null
      wasCancelled = true
    }

    if (this.currentExtraProcessingAbortController) {
      this.currentExtraProcessingAbortController.abort()
      this.currentExtraProcessingAbortController = null
      wasCancelled = true
    }

    // Reset hasDebugged flag
    this.deps.setHasDebugged(false)

    // Clear any pending state
    this.deps.setProblemInfo(null)

    const mainWindow = this.deps.getMainWindow()
    if (wasCancelled && mainWindow && !mainWindow.isDestroyed()) {
      // Send a clear message that processing was cancelled
      mainWindow.webContents.send(this.deps.PROCESSING_EVENTS.NO_SCREENSHOTS)
    }
  }
}
