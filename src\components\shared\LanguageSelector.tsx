import React from "react"

interface LanguageSelectorProps {
  currentLanguage: string
  setLanguage: (language: string) => void
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  currentLanguage,
  setLanguage
}) => {
  const handleLanguageChange = async (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    const newLanguage = e.target.value
    // No need to save to database - just update local state
    window.__LANGUAGE__ = newLanguage
    setLanguage(newLanguage)
  }

  const handleFocus = () => {
    // Temporarily increase window height when select is focused
    // to ensure dropdown is visible
    const selectElement = document.querySelector('select') as HTMLSelectElement
    if (selectElement) {
      const rect = selectElement.getBoundingClientRect()
      const optionCount = selectElement.options.length
      const optionHeight = 25 // Approximate height per option
      const dropdownHeight = Math.min(optionCount * optionHeight + 20, 350) // Cap at 350px
      
      // Force window to accommodate dropdown
      window.electronAPI?.updateContentDimensions({
        width: Math.max(window.innerWidth, 400),
        height: Math.max(rect.bottom + dropdownHeight, 600)
      })
    }
  }

  const handleBlur = () => {
    // Reset window size after selection with a delay
    setTimeout(() => {
      const contentElement = document.querySelector('.bg-transparent')
      if (contentElement) {
        window.electronAPI?.updateContentDimensions({
          width: contentElement.scrollWidth,
          height: contentElement.scrollHeight
        })
      }
    }, 200)
  }

  return (
    <div className="mb-3 px-2 space-y-1">
      <div className="flex items-center justify-between text-[13px] font-medium text-white/90">
        <span>Language</span>
        <select
          value={currentLanguage}
          onChange={handleLanguageChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          className="bg-white/10 rounded px-2 py-1 text-sm outline-none border border-white/10 focus:border-white/20 cursor-pointer appearance-none text-white hover:bg-white/20 transition-colors"
          style={{ 
            backgroundImage: `url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23ffffff' d='M10.293 3.293L6 7.586 1.707 3.293A1 1 0 00.293 4.707l5 5a1 1 0 001.414 0l5-5a1 1 0 10-1.414-1.414z'/%3E%3C/svg%3E")`,
            backgroundPosition: 'right 8px center',
            backgroundRepeat: 'no-repeat',
            paddingRight: '28px',
            minWidth: '150px'
          }}
        >
          <option value="python" className="bg-gray-800 text-white">Python</option>
          <option value="javascript" className="bg-gray-800 text-white">JavaScript</option>
          <option value="java" className="bg-gray-800 text-white">Java</option>
          <option value="golang" className="bg-gray-800 text-white">Go</option>
          <option value="csharp" className="bg-gray-800 text-white">C#</option>
          <option value="rust" className="bg-gray-800 text-white">Rust</option>
          <option value="cpp" className="bg-gray-800 text-white">C++</option>
          <option value="swift" className="bg-gray-800 text-white">Swift</option>
          <option value="kotlin" className="bg-gray-800 text-white">Kotlin</option>
          <option value="ruby" className="bg-gray-800 text-white">Ruby</option>
          <option value="sql" className="bg-gray-800 text-white">SQL</option>
          <option value="r" className="bg-gray-800 text-white">R</option>
        </select>
      </div>
    </div>
  )
}