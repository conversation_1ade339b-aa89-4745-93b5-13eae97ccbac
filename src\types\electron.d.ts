export interface ElectronAPI {
  // Subscription portal removed - no authentication needed
  updateContentDimensions: (dimensions: {
    width: number
    height: number
  }) => Promise<void>
  clearStore: () => Promise<{ success: boolean; error?: string }>
  getScreenshots: () => Promise<{
    success: boolean
    previews?: Array<{ path: string; preview: string }> | null
    error?: string
  }>
  deleteScreenshot: (
    path: string
  ) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (
    callback: (data: { path: string; preview: string }) => void
  ) => () => void
  onResetView: (callback: () => void) => () => void
  onSolutionStart: (callback: () => void) => () => void
  onDebugStart: (callback: () => void) => () => void
  onDebugSuccess: (callback: (data: any) => void) => () => void
  onSolutionError: (callback: (error: string) => void) => () => void
  onProcessingNoScreenshots: (callback: () => void) => () => void
  onProblemExtracted: (callback: (data: any) => void) => () => void
  onSolutionSuccess: (callback: (data: any) => void) => () => void
  // Unauthorized handler removed - no authentication needed
  onDebugError: (callback: (error: string) => void) => () => void
  openExternal: (url: string) => void
  toggleMainWindow: () => Promise<{ success: boolean; error?: string }>
  triggerScreenshot: () => Promise<{ success: boolean; error?: string }>
  triggerProcessScreenshots: () => Promise<{ success: boolean; error?: string }>
  triggerReset: () => Promise<{ success: boolean; error?: string }>
  triggerMoveLeft: () => Promise<{ success: boolean; error?: string }>
  triggerMoveRight: () => Promise<{ success: boolean; error?: string }>
  triggerMoveUp: () => Promise<{ success: boolean; error?: string }>
  triggerMoveDown: () => Promise<{ success: boolean; error?: string }>
  // Subscription callbacks removed - no authentication needed
  startUpdate: () => Promise<{ success: boolean; error?: string }>
  installUpdate: () => void
  onUpdateAvailable: (callback: (info: any) => void) => () => void
  onUpdateDownloaded: (callback: (info: any) => void) => () => void

  // Credits methods removed - unlimited usage
  getPlatform: () => string

  // AI Configuration methods
  getAIConfig: () => Promise<any>
  updateAIConfig: (config: any) => Promise<{ success: boolean; error?: string }>
  resetAIConfig: () => Promise<{ success: boolean; error?: string }>
  optimizeAIConfig: () => Promise<{ success: boolean; error?: string }>
  testAIConnection: () => Promise<{ success: boolean; error?: string }>
  getAvailableModels: () => Promise<{ success: boolean; models?: string[]; error?: string }>
  clearModelsCache: () => Promise<{ success: boolean; error?: string }>
  clearModelsCache: () => Promise<{ success: boolean; error?: string }>

  // Performance monitoring methods
  getPerformanceStats: () => Promise<any>
  getPerformanceReport: () => Promise<string>
  clearPerformanceData: () => Promise<{ success: boolean; error?: string }>

  // Additional screenshot methods
  takeScreenshot: () => Promise<{ success: boolean; error?: string }>
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    electron: {
      ipcRenderer: {
        on: (channel: string, func: (...args: any[]) => void) => void
        removeListener: (
          channel: string,
          func: (...args: any[]) => void
        ) => void
      }
    }
    __CREDITS__: number
  }
}
