// CacheManager.ts - 智能缓存管理

import * as fs from 'fs'
import * as path from 'path'
import { app } from 'electron'
import crypto from 'crypto'

export interface CacheEntry<T> {
  data: T
  timestamp: number
  expiresAt: number
  hash: string
}

export interface CacheConfig {
  maxAge: number // 缓存最大存活时间（毫秒）
  maxSize: number // 最大缓存条目数
  cleanupInterval: number // 清理间隔（毫秒）
}

export class CacheManager<T> {
  private cache = new Map<string, CacheEntry<T>>()
  private cacheDir: string
  private config: CacheConfig
  private cleanupTimer?: NodeJS.Timeout

  constructor(
    cacheName: string,
    config: Partial<CacheConfig> = {}
  ) {
    this.config = {
      maxAge: 24 * 60 * 60 * 1000, // 默认24小时
      maxSize: 100, // 默认最多100个条目
      cleanupInterval: 60 * 60 * 1000, // 默认1小时清理一次
      ...config
    }

    this.cacheDir = path.join(app.getPath('userData'), 'cache', cacheName)
    this.ensureCacheDir()
    this.loadFromDisk()
    this.startCleanupTimer()
  }

  /**
   * 生成缓存键的哈希值
   */
  private generateHash(key: string): string {
    return crypto.createHash('sha256').update(key).digest('hex').substring(0, 16)
  }

  /**
   * 确保缓存目录存在
   */
  private ensureCacheDir(): void {
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true })
    }
  }

  /**
   * 从磁盘加载缓存
   */
  private loadFromDisk(): void {
    try {
      const files = fs.readdirSync(this.cacheDir)
      const now = Date.now()

      for (const file of files) {
        if (!file.endsWith('.json')) continue

        try {
          const filePath = path.join(this.cacheDir, file)
          const content = fs.readFileSync(filePath, 'utf-8')
          const entry: CacheEntry<T> = JSON.parse(content)

          // 检查是否过期
          if (entry.expiresAt > now) {
            const key = file.replace('.json', '')
            this.cache.set(key, entry)
          } else {
            // 删除过期文件
            fs.unlinkSync(filePath)
          }
        } catch (error) {
          console.error(`Error loading cache file ${file}:`, error)
        }
      }

      console.log(`Loaded ${this.cache.size} cache entries from disk`)
    } catch (error) {
      console.error('Error loading cache from disk:', error)
    }
  }

  /**
   * 保存缓存条目到磁盘
   */
  private saveToDisk(key: string, entry: CacheEntry<T>): void {
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`)
      fs.writeFileSync(filePath, JSON.stringify(entry), 'utf-8')
    } catch (error) {
      console.error(`Error saving cache entry ${key} to disk:`, error)
    }
  }

  /**
   * 从磁盘删除缓存条目
   */
  private removeFromDisk(key: string): void {
    try {
      const filePath = path.join(this.cacheDir, `${key}.json`)
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath)
      }
    } catch (error) {
      console.error(`Error removing cache entry ${key} from disk:`, error)
    }
  }

  /**
   * 启动定期清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 停止清理定时器
   */
  public stopCleanupTimer(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
  }

  /**
   * 清理过期和超量的缓存条目
   */
  public cleanup(): void {
    const now = Date.now()
    const expiredKeys: string[] = []

    // 找出过期的条目
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt <= now) {
        expiredKeys.push(key)
      }
    }

    // 删除过期条目
    for (const key of expiredKeys) {
      this.cache.delete(key)
      this.removeFromDisk(key)
    }

    // 如果缓存条目仍然太多，删除最旧的条目
    if (this.cache.size > this.config.maxSize) {
      const entries = Array.from(this.cache.entries())
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp)

      const toRemove = entries.slice(0, this.cache.size - this.config.maxSize)
      for (const [key] of toRemove) {
        this.cache.delete(key)
        this.removeFromDisk(key)
      }
    }

    if (expiredKeys.length > 0) {
      console.log(`Cleaned up ${expiredKeys.length} expired cache entries`)
    }
  }

  /**
   * 设置缓存条目
   */
  public set(key: string, data: T, customMaxAge?: number): void {
    const hash = this.generateHash(key)
    const now = Date.now()
    const maxAge = customMaxAge || this.config.maxAge

    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      expiresAt: now + maxAge,
      hash
    }

    this.cache.set(hash, entry)
    this.saveToDisk(hash, entry)

    // 如果缓存太大，触发清理
    if (this.cache.size > this.config.maxSize) {
      this.cleanup()
    }
  }

  /**
   * 获取缓存条目
   */
  public get(key: string): T | null {
    const hash = this.generateHash(key)
    const entry = this.cache.get(hash)

    if (!entry) {
      return null
    }

    // 检查是否过期
    if (entry.expiresAt <= Date.now()) {
      this.cache.delete(hash)
      this.removeFromDisk(hash)
      return null
    }

    return entry.data
  }

  /**
   * 检查缓存是否存在且未过期
   */
  public has(key: string): boolean {
    return this.get(key) !== null
  }

  /**
   * 删除特定缓存条目
   */
  public delete(key: string): boolean {
    const hash = this.generateHash(key)
    const existed = this.cache.has(hash)
    
    if (existed) {
      this.cache.delete(hash)
      this.removeFromDisk(hash)
    }

    return existed
  }

  /**
   * 清空所有缓存
   */
  public clear(): void {
    // 清空内存缓存
    this.cache.clear()

    // 清空磁盘缓存
    try {
      const files = fs.readdirSync(this.cacheDir)
      for (const file of files) {
        if (file.endsWith('.json')) {
          fs.unlinkSync(path.join(this.cacheDir, file))
        }
      }
    } catch (error) {
      console.error('Error clearing disk cache:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  public getStats(): {
    size: number
    maxSize: number
    oldestEntry: number | null
    newestEntry: number | null
  } {
    const entries = Array.from(this.cache.values())
    const timestamps = entries.map(e => e.timestamp)

    return {
      size: this.cache.size,
      maxSize: this.config.maxSize,
      oldestEntry: timestamps.length > 0 ? Math.min(...timestamps) : null,
      newestEntry: timestamps.length > 0 ? Math.max(...timestamps) : null
    }
  }

  /**
   * 销毁缓存管理器
   */
  public destroy(): void {
    this.stopCleanupTimer()
    this.cache.clear()
  }
}
