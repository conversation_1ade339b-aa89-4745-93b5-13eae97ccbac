// ErrorHandler.ts - 统一错误处理和分类

export enum AIErrorType {
  NETWORK_ERROR = 'network',
  API_KEY_ERROR = 'api_key',
  RATE_LIMIT_ERROR = 'rate_limit',
  PARSING_ERROR = 'parsing',
  TIMEOUT_ERROR = 'timeout',
  QUOTA_EXCEEDED = 'quota_exceeded',
  MODEL_ERROR = 'model_error',
  IMAGE_ERROR = 'image_error',
  UNKNOWN_ERROR = 'unknown'
}

export interface AIError {
  type: AIErrorType
  message: string
  originalError?: any
  retryable: boolean
  userMessage: string
  suggestedAction?: string
}

export class ErrorHandler {
  /**
   * 分析错误并返回结构化的错误信息
   */
  static analyzeError(error: any): AIError {
    const errorMessage = error?.message || error?.toString() || 'Unknown error'
    const errorCode = error?.code || error?.status || error?.response?.status

    // API 密钥相关错误
    if (errorMessage.includes('API key') ||
        errorMessage.includes('Unauthorized') ||
        errorCode === 401) {
      return {
        type: AIErrorType.API_KEY_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: false,
        userMessage: 'API 密钥无效或未设置',
        suggestedAction: '请在设置面板的AI设置中配置正确的API密钥'
      }
    }

    // 速率限制错误
    if (errorMessage.includes('rate limit') || 
        errorMessage.includes('Too Many Requests') || 
        errorCode === 429) {
      return {
        type: AIErrorType.RATE_LIMIT_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: true,
        userMessage: 'API 调用频率过高',
        suggestedAction: '请稍等片刻后重试'
      }
    }

    // 配额超限错误
    if (errorMessage.includes('quota') || 
        errorMessage.includes('billing') ||
        errorMessage.includes('insufficient_quota')) {
      return {
        type: AIErrorType.QUOTA_EXCEEDED,
        message: errorMessage,
        originalError: error,
        retryable: false,
        userMessage: 'API 配额已用完',
        suggestedAction: '请检查您的 API 账户余额或升级套餐'
      }
    }

    // 网络相关错误
    if (errorMessage.includes('network') || 
        errorMessage.includes('timeout') ||
        errorMessage.includes('ECONNRESET') ||
        errorMessage.includes('ENOTFOUND') ||
        errorCode === 'ECONNRESET' ||
        errorCode === 'ENOTFOUND') {
      return {
        type: AIErrorType.NETWORK_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: true,
        userMessage: '网络连接问题',
        suggestedAction: '请检查网络连接后重试'
      }
    }

    // 超时错误
    if (errorMessage.includes('timeout') || errorCode === 'TIMEOUT') {
      return {
        type: AIErrorType.TIMEOUT_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: true,
        userMessage: '请求超时',
        suggestedAction: '请稍后重试，或检查网络连接'
      }
    }

    // 模型相关错误
    if (errorMessage.includes('model') || 
        errorMessage.includes('not found') ||
        errorCode === 404) {
      return {
        type: AIErrorType.MODEL_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: false,
        userMessage: '模型不可用',
        suggestedAction: '请检查 .env 文件中的 OPENAI_MODEL 设置'
      }
    }

    // JSON 解析错误
    if (errorMessage.includes('JSON') || 
        errorMessage.includes('parse') ||
        errorMessage.includes('SyntaxError')) {
      return {
        type: AIErrorType.PARSING_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: true,
        userMessage: 'AI 响应格式错误',
        suggestedAction: '请重试，如果问题持续存在请联系支持'
      }
    }

    // 图片相关错误
    if (errorMessage.includes('image') || 
        errorMessage.includes('screenshot') ||
        errorMessage.includes('sharp')) {
      return {
        type: AIErrorType.IMAGE_ERROR,
        message: errorMessage,
        originalError: error,
        retryable: true,
        userMessage: '图片处理失败',
        suggestedAction: '请重新截图后重试'
      }
    }

    // 默认未知错误
    return {
      type: AIErrorType.UNKNOWN_ERROR,
      message: errorMessage,
      originalError: error,
      retryable: true,
      userMessage: '处理过程中出现未知错误',
      suggestedAction: '请重试，如果问题持续存在请联系支持'
    }
  }

  /**
   * 获取用户友好的错误消息
   */
  static getUserFriendlyMessage(error: any): string {
    const analyzedError = this.analyzeError(error)
    return `${analyzedError.userMessage}${analyzedError.suggestedAction ? ` - ${analyzedError.suggestedAction}` : ''}`
  }

  /**
   * 判断错误是否可以重试
   */
  static isRetryable(error: any): boolean {
    const analyzedError = this.analyzeError(error)
    return analyzedError.retryable
  }

  /**
   * 获取重试延迟时间（毫秒）
   */
  static getRetryDelay(attemptNumber: number, errorType: AIErrorType): number {
    const baseDelay = 1000 // 1秒基础延迟

    switch (errorType) {
      case AIErrorType.RATE_LIMIT_ERROR:
        // 速率限制：指数退避，最长60秒
        return Math.min(baseDelay * Math.pow(2, attemptNumber), 60000)
      
      case AIErrorType.NETWORK_ERROR:
      case AIErrorType.TIMEOUT_ERROR:
        // 网络/超时：线性增长，最长30秒
        return Math.min(baseDelay * (attemptNumber + 1), 30000)
      
      default:
        // 其他错误：固定延迟
        return baseDelay * 2
    }
  }

  /**
   * 记录错误日志
   */
  static logError(error: AIError, context?: string): void {
    const logMessage = `[${error.type}] ${context ? `${context}: ` : ''}${error.message}`
    console.error(logMessage)
    
    if (error.originalError) {
      console.error('Original error:', error.originalError)
    }
  }
}
