import React, { useState, useCallback } from 'react'
import CustomAlert from '../components/shared/CustomAlert'

interface AlertOptions {
  title?: string
  message: string
  type?: 'success' | 'error' | 'warning' | 'info'
  confirmText?: string
  cancelText?: string
  showCancel?: boolean
}

interface AlertState extends AlertOptions {
  isOpen: boolean
  onConfirm: () => void
  onCancel?: () => void
}

export const useCustomAlert = () => {
  const [alertState, setAlertState] = useState<AlertState>({
    isOpen: false,
    message: '',
    onConfirm: () => {}
  })

  const showAlert = useCallback((options: AlertOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setAlertState({
        ...options,
        isOpen: true,
        onConfirm: () => {
          setAlertState(prev => ({ ...prev, isOpen: false }))
          resolve(true)
        },
        onCancel: options.showCancel ? () => {
          setAlertState(prev => ({ ...prev, isOpen: false }))
          resolve(false)
        } : undefined
      })
    })
  }, [])

  const showSuccess = useCallback((message: string, title?: string) => {
    return showAlert({ message, title, type: 'success' })
  }, [showAlert])

  const showError = useCallback((message: string, title?: string) => {
    return showAlert({ message, title, type: 'error' })
  }, [showAlert])

  const showWarning = useCallback((message: string, title?: string) => {
    return showAlert({ message, title, type: 'warning' })
  }, [showAlert])

  const showInfo = useCallback((message: string, title?: string) => {
    return showAlert({ message, title, type: 'info' })
  }, [showAlert])

  const showConfirm = useCallback((message: string, title?: string) => {
    return showAlert({ 
      message, 
      title, 
      type: 'warning',
      showCancel: true,
      confirmText: '确定',
      cancelText: '取消'
    })
  }, [showAlert])

  const AlertComponent = useCallback(() => (
    <CustomAlert
      isOpen={alertState.isOpen}
      title={alertState.title}
      message={alertState.message}
      type={alertState.type}
      onConfirm={alertState.onConfirm}
      onCancel={alertState.onCancel}
      confirmText={alertState.confirmText}
      cancelText={alertState.cancelText}
    />
  ), [alertState])

  return {
    showAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    AlertComponent
  }
}
