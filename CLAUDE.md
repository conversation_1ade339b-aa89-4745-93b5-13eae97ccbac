# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Interview Coder is an Electron-based desktop application designed to help users during technical interviews. It features an invisible window that can capture screenshots, analyze coding problems using AI, and provide solutions.

## Tech Stack

- **Frontend**: React 18 with TypeScript
- **Desktop Framework**: Electron 29
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI
- **Backend/Auth**: Supabase
- **AI Integration**: OpenAI API
- **State Management**: React Query

## Key Commands

### Development
```bash
# Install dependencies
npm install

# Run in development mode (starts Vite + Electron with hot reload)
npm run dev

# Clean build artifacts
npm run clean
```

### Building
```bash
# Build for production (creates distributable)
npm run build

# After building on macOS (for notarization)
node scripts/manual-notarize.js "release/Interview-Coder-x64.dmg" && xcrun stapler staple "release/Interview-Coder-x64.dmg"
node scripts/manual-notarize.js "release/Interview-Coder-arm64.dmg" && xcrun stapler staple "release/Interview-Coder-arm64.dmg"
```

## Architecture

### Main Process (Electron)
- **Entry**: `electron/main.ts` → compiles to `dist-electron/main.js`
- **Key Modules**:
  - `ScreenshotHelper.ts`: Handles screenshot capture functionality
  - `ProcessingHelper.ts`: Processes screenshots and interfaces with AI
  - `ipcHandlers.ts`: IPC communication between main and renderer
  - `shortcuts.ts`: Global keyboard shortcuts management
  - `store.ts`: Electron store for persistent settings
  - `autoUpdater.ts`: Auto-update functionality

### Renderer Process (React)
- **Entry**: `src/main.tsx`
- **Pages** (`src/_pages/`):
  - `Queue.tsx`: Screenshot queue management
  - `Solutions.tsx`: Solution viewing and debugging
  - `SubscribePage.tsx`: Subscription management
  - `Debug.tsx`: Debugging interface
- **Components** (`src/components/`):
  - `Queue/`: Screenshot queue components
  - `Solutions/`: Solution display components
  - `ui/`: Reusable UI components (button, card, dialog, etc.)

### Global Shortcuts
- Toggle Window: `Ctrl/Cmd + B`
- Take Screenshot: `Ctrl/Cmd + H`
- Process Screenshots: `Ctrl/Cmd + Enter`
- Move Window: `Ctrl/Cmd + Arrow Keys`
- Reset View: `Ctrl/Cmd + R`
- Quit: `Ctrl/Cmd + Q`

## Important Considerations

### Window Invisibility
The application uses special window properties to remain invisible to certain screen capture methods. This is implemented in the main process window creation.

### Screenshot Processing Flow
1. User captures screenshot via global shortcut
2. Screenshot saved to queue (max 2 items)
3. Processing triggered to extract text/code
4. AI analyzes content and generates solutions
5. Solutions displayed in the renderer

### IPC Communication
All communication between main and renderer processes goes through predefined IPC channels. Key channels include:
- `screenshot:capture`
- `screenshot:process`
- `solution:generate`
- `window:toggle`

### Build Configuration
- Output directory: `release/`
- Electron Builder config in `package.json`
- Platform-specific builds for macOS (x64/arm64), Windows (NSIS), Linux (AppImage)